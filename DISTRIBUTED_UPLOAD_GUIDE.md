# 分布式上传功能指南

## 概述

为了解决400名学生同时上传视频造成的瞬时高负载问题，我们实现了分布式上传功能。该功能将视频上传分布在考试过程中，而不是集中在考试结束时。

## 问题分析

### 传统上传方式的问题
- **瞬时高负载**：400名学生同时上传，服务器压力巨大
- **网络拥塞**：大量并发上传导致网络带宽不足
- **上传失败**：高负载下容易出现上传超时和失败
- **用户体验差**：考试结束时需要等待很长时间

### 分布式上传的优势
- **负载分散**：上传请求分散在考试过程中
- **网络优化**：避免网络拥塞
- **成功率提升**：降低服务器压力，提高上传成功率
- **实时监控**：考试过程中就能看到录屏文件

## 技术实现

### 核心机制
1. **上传队列**：录制的视频先存入本地队列
2. **定时上传**：按配置的时间间隔上传队列中的文件
3. **随机延迟**：添加随机延迟避免同时上传
4. **批量处理**：支持批量上传多个文件

### 配置参数
```json
{
  "upload": {
    "strategy": "distributed",    // 上传策略
    "interval": 120,              // 上传间隔（秒）
    "max_delay": 60,              // 最大随机延迟（秒）
    "batch_size": 5,              // 批量上传大小
    "retry_count": 3,             // 重试次数
    "timeout": 60                 // 上传超时时间（秒）
  }
}
```

## 使用配置

### 快速配置
```bash
# 创建默认配置文件
python screen_recording_config.py create

# 查看配置帮助
python screen_recording_config.py help
```

### 根据学生数量优化配置

#### 400名学生（大量）
```json
{
  "upload": {
    "strategy": "distributed",
    "interval": 180,              // 3分钟上传一次
    "max_delay": 120,             // 最大延迟2分钟
    "batch_size": 3,
    "retry_count": 3,
    "timeout": 60
  },
  "performance": {
    "max_concurrent_uploads": 2,  // 最大并发上传数
    "upload_delay": 2             // 上传间隔2秒
  }
}
```

#### 200名学生（中等）
```json
{
  "upload": {
    "strategy": "distributed",
    "interval": 150,              // 2.5分钟上传一次
    "max_delay": 90,              // 最大延迟1.5分钟
    "batch_size": 4,
    "retry_count": 3,
    "timeout": 60
  },
  "performance": {
    "max_concurrent_uploads": 3,
    "upload_delay": 1.5
  }
}
```

#### 100名学生（少量）
```json
{
  "upload": {
    "strategy": "distributed",
    "interval": 120,              // 2分钟上传一次
    "max_delay": 60,              // 最大延迟1分钟
    "batch_size": 5,
    "retry_count": 3,
    "timeout": 60
  },
  "performance": {
    "max_concurrent_uploads": 5,
    "upload_delay": 1
  }
}
```

## 使用方法

### 1. 安装依赖
```bash
python install_screen_recording.py
```

### 2. 配置上传策略
```bash
# 编辑配置文件
python screen_recording_config.py
```

### 3. 启动系统
```bash
# 启动服务器
python server.py

# 启动客户端
python main.py
```

### 4. 监控上传性能
```bash
# 实时监控
python upload_performance_monitor.py --action monitor

# 模拟负载测试
python upload_performance_monitor.py --action simulate --students 400 --duration 2

# 生成性能报告
python upload_performance_monitor.py --action report
```

## 性能对比

### 传统上传方式
- **瞬时并发**：400个同时上传
- **网络带宽**：需要大量带宽
- **服务器负载**：极高
- **成功率**：约60-70%
- **用户体验**：考试结束需要等待

### 分布式上传方式
- **瞬时并发**：约10-20个同时上传
- **网络带宽**：带宽需求分散
- **服务器负载**：中等且稳定
- **成功率**：约95-98%
- **用户体验**：考试过程中实时上传

## 时间分布示例

### 2小时考试，400名学生的时间分布

| 时间点 | 上传学生数 | 说明 |
|--------|------------|------|
| 0-2分钟 | 0-20 | 随机延迟阶段 |
| 2-4分钟 | 20-40 | 第一批上传 |
| 4-6分钟 | 0-20 | 随机延迟 |
| 6-8分钟 | 20-40 | 第二批上传 |
| ... | ... | 持续分布 |
| 118-120分钟 | 20-40 | 最后一批上传 |

### 上传时间表（示例）
```
考试开始: 09:00:00
第一批上传: 09:02:30 ± 60秒随机延迟
第二批上传: 09:05:30 ± 60秒随机延迟
第三批上传: 09:08:30 ± 60秒随机延迟
...
最后一批上传: 10:58:30 ± 60秒随机延迟
考试结束: 11:00:00
```

## 监控和调试

### 实时监控
```bash
python upload_performance_monitor.py --action monitor
```

输出示例：
```
[14:30:15] 上传性能状态:
  总上传次数: 156
  成功上传: 152
  失败上传: 4
  总上传大小: 2.3 GB
  当前并发上传: 8
  队列大小: 12
  服务器负载: 0
  网络使用率: 45.2%
```

### 性能报告
```bash
python upload_performance_monitor.py --action report
```

生成文件：
- `upload_performance_report.json` - 详细性能数据
- `upload_performance_charts.png` - 性能图表

### 图表分析
- **每小时上传次数**：显示上传分布情况
- **每小时上传大小**：显示带宽使用情况
- **上传时间分布**：分析上传性能
- **文件大小分布**：了解文件特征

## 故障排除

### 常见问题

1. **上传失败率高**
   - 检查网络连接
   - 增加重试次数
   - 延长超时时间
   - 减少并发上传数

2. **上传间隔太短**
   - 增加 `interval` 参数
   - 增加 `max_delay` 参数
   - 减少 `batch_size` 参数

3. **服务器负载高**
   - 减少 `max_concurrent_uploads`
   - 增加 `upload_delay`
   - 增加 `interval`

4. **网络带宽不足**
   - 降低视频质量
   - 减少录制帧率
   - 增加上传间隔

### 调试命令
```bash
# 检查配置
python screen_recording_config.py

# 测试API
python test_screen_recording.py

# 监控性能
python upload_performance_monitor.py --action monitor

# 查看日志
tail -f logs/user_username_2023-12-01.log
```

## 最佳实践

### 1. 根据环境调整配置
- **高配置服务器**：可以增加并发数和减少间隔
- **低配置服务器**：减少并发数，增加间隔
- **网络环境好**：可以减少延迟和间隔
- **网络环境差**：增加延迟和间隔

### 2. 监控和优化
- 定期查看性能报告
- 根据实际情况调整参数
- 监控服务器资源使用情况
- 关注网络带宽使用

### 3. 备份和恢复
- 定期备份录屏文件
- 设置自动清理策略
- 监控磁盘空间使用
- 准备故障恢复方案

## 总结

分布式上传功能有效解决了大量学生同时上传的问题：

✅ **负载分散**：避免瞬时高负载
✅ **网络优化**：合理利用网络带宽
✅ **成功率提升**：显著提高上传成功率
✅ **用户体验**：考试过程中实时上传
✅ **可配置性**：根据实际情况灵活调整
✅ **可监控性**：提供完整的性能监控

通过合理配置，400名学生的录屏上传可以平稳分布在2小时的考试过程中，避免考试结束时的网络拥塞和服务器压力。 