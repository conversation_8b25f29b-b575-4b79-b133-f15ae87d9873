# 考试文件API修改

## 修改概述

将服务器端的截图和违规记录API修改为基于exam_id的路径结构，实现更好的文件组织和管理。

## API路径变更

### 1. 截图文件API

**修改前:**
```
/screenshots/<filename>
```

**修改后:**
```
/screenshots/<int:exam_id>/<filename>
```

**示例:**
- 旧格式: `/screenshots/screenshot_2023001_1_2024-01-15_10-30-00.png`
- 新格式: `/screenshots/1/screenshot_2023001_1_2024-01-15_10-30-00.png`

### 2. 违规记录API

**已经是正确格式:**
```
/api/exams/<int:exam_id>/violations
```

**示例:**
- `/api/exams/1/violations`
- `/api/exams/1/violations?page=1&per_page=12`

## 服务器端修改

### 1. 截图服务路由 (`server.py`)

**修改内容:**
```python
@app.route('/screenshots/<int:exam_id>/<filename>')
def serve_screenshot(exam_id, filename):
    """提供截图文件"""
    try:
        # 验证考试是否存在
        exam = data_access.get_exam(exam_id)
        if not exam:
            return jsonify({"status": "error", "message": "考试不存在"}), 404
        
        # 构建截图文件目录路径
        screenshots_path = os.path.join(DATA_DIR, str(exam_id), "screenshots")
        
        # 检查目录和文件是否存在
        if not os.path.exists(screenshots_path):
            return jsonify({"status": "error", "message": "截图目录不存在"}), 404
        
        file_path = os.path.join(screenshots_path, filename)
        if not os.path.exists(file_path):
            return jsonify({"status": "error", "message": "截图文件不存在"}), 404
        
        return send_from_directory(screenshots_path, filename)
        
    except Exception as e:
        return jsonify({"status": "error", "message": f"获取截图文件失败: {str(e)}"}), 500
```

**新增功能:**
- 考试存在性验证
- 目录和文件存在性检查
- 详细的错误处理
- 基于exam_id的文件路径组织

### 2. 截图上传处理

**修改内容:**
```python
# 保存截图文件
filename = f"screenshot_{student_id}_{exam_id}_{timestamp.replace(' ', '_').replace(':', '-')}.png"

# 创建考试专用的截图目录
exam_screenshot_dir = os.path.join(DATA_DIR, str(exam_id), "screenshots")
if not os.path.exists(exam_screenshot_dir):
    os.makedirs(exam_screenshot_dir)

screenshot_path = os.path.join(exam_screenshot_dir, filename)
screenshot.save(screenshot_path)
```

**改进:**
- 自动创建考试专用目录
- 基于exam_id的目录结构
- 确保目录存在

### 3. 录屏文件处理

**修改内容:**
```python
@app.route('/api/exams/<int:exam_id>/students/<student_id>/recordings')
def get_student_recordings(exam_id, student_id):
    """获取指定考生的所有录屏文件（片段和合并后的）"""
    # 创建考试专用的录屏目录路径
    exam_recordings_dir = os.path.join(DATA_DIR, str(exam_id), "recordings")
    
    if not os.path.exists(exam_recordings_dir):
        return jsonify({"recordings": []})
    
    # ... 其余逻辑
```

## 数据访问层修改

### 1. 违规记录查询 (`data_access.py`)

**修改内容:**
```python
def get_exam_violations(self, exam_id, page=1, per_page=12):
    conn = self.get_connection()
    try:
        with conn.cursor(dictionary=True) as cursor:
            offset = (page - 1) * per_page
            sql = "SELECT * FROM violations WHERE exam_id=%s ORDER BY timestamp DESC LIMIT %s OFFSET %s"
            cursor.execute(sql, (exam_id, per_page, offset))
            violations = cursor.fetchall()
            
            # 为每个违规记录生成正确的截图URL
            for violation in violations:
                if violation.get('screenshot_path'):
                    # 从完整路径中提取文件名
                    import os
                    filename = os.path.basename(violation['screenshot_path'])
                    # 生成新的URL格式: /screenshots/{exam_id}/{filename}
                    violation['screenshot_url'] = f"/screenshots/{exam_id}/{filename}"
                else:
                    violation['screenshot_url'] = None
                
                # 格式化时间戳
                if violation.get('timestamp'):
                    if hasattr(violation['timestamp'], 'strftime'):
                        violation['timestamp'] = violation['timestamp'].strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        violation['timestamp'] = str(violation['timestamp'])
            
            return violations
    finally:
        conn.close()
```

**新增功能:**
- 自动生成正确格式的截图URL
- 基于exam_id的URL路径
- 时间戳格式化
- 文件名提取处理

## 文件目录结构

### 新的目录组织

```
server_data/
├── 1/                          # 考试ID为1
│   ├── screenshots/            # 截图文件
│   │   ├── screenshot_2023001_1_2024-01-15_10-30-00.png
│   │   └── screenshot_2023002_1_2024-01-15_10-31-00.png
│   └── recordings/             # 录屏文件
│       ├── recording_2023001_1_segment1.mp4
│       └── recording_2023001_1_merged.mp4
├── 2/                          # 考试ID为2
│   ├── screenshots/
│   └── recordings/
└── 3/                          # 考试ID为3
    ├── screenshots/
    └── recordings/
```

### 优势

1. **清晰的组织结构**: 每个考试有独立的目录
2. **易于管理**: 可以按考试删除或备份文件
3. **避免冲突**: 不同考试的文件不会混淆
4. **扩展性好**: 可以轻松添加其他类型的文件

## 前端兼容性

### index.html 无需修改

前端代码已经使用正确的API路径：

```javascript
// 违规记录API调用
$.ajax({
    url: `/api/exams/${currentExamId}/violations`,
    method: 'GET',
    // ...
});

// 截图URL使用
violations.forEach(v => {
    // v.screenshot_url 已经是正确的格式: /screenshots/{exam_id}/{filename}
    const item = $(`
        <img src="${v.screenshot_url}" alt="违规截图" 
            onclick="showBigScreenshot('${v.screenshot_url}')"
            loading="lazy">
    `);
});
```

## 错误处理改进

### 1. 考试验证
- 验证exam_id对应的考试是否存在
- 返回404错误如果考试不存在

### 2. 文件验证
- 检查目录是否存在
- 检查文件是否存在
- 返回具体的错误信息

### 3. 异常处理
- 捕获所有可能的异常
- 返回友好的错误消息
- 记录详细的错误信息

## 测试验证

### 运行测试脚本
```bash
python test_exam_file_apis.py [服务器地址] [考试ID] [测试文件名]
```

### 测试内容
- ✅ 考试存在性验证
- ✅ 违规记录API功能
- ✅ 违规记录分页功能
- ✅ 截图URL格式验证
- ✅ 文件访问权限检查

## 迁移指南

### 1. 现有文件迁移
如果有现有的截图文件，需要按考试ID重新组织：

```bash
# 创建新的目录结构
mkdir -p server_data/1/screenshots
mkdir -p server_data/1/recordings

# 移动现有文件（示例）
mv old_screenshots/screenshot_*_1_*.png server_data/1/screenshots/
mv old_recordings/recording_*_1_*.mp4 server_data/1/recordings/
```

### 2. 数据库更新
确保violations表中的screenshot_path字段包含正确的文件路径。

### 3. 配置检查
- 确保DATA_DIR配置正确
- 检查文件权限设置
- 验证目录创建权限

## 总结

通过这次修改，实现了：

### ✅ 核心改进:
- 基于exam_id的文件路径结构
- 更好的文件组织和管理
- 完善的错误处理机制
- 自动目录创建功能

### ✅ API优化:
- 截图API: `/screenshots/{exam_id}/{filename}`
- 违规API: `/api/exams/{exam_id}/violations`
- 录屏API: `/api/exams/{exam_id}/students/{student_id}/recordings`

### ✅ 系统优势:
- 清晰的文件组织结构
- 易于维护和扩展
- 更好的安全性验证
- 完整的错误处理

这个修改为考试监控系统提供了更加健壮和可维护的文件管理架构！
