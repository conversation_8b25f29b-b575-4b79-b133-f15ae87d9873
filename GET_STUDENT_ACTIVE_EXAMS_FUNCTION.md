# get_student_active_exams 函数实现

## 函数概述

`get_student_active_exams(student_id)` 函数用于根据学号返回学生可以参加的正在进行的考试。

## 函数签名

```python
def get_student_active_exams(self, student_id):
    """根据学号返回学生可以参加的正在进行的考试"""
```

## 参数

- **student_id** (str): 学生的学号

## 返回值

- **list**: 活跃考试列表，每个考试包含完整的考试信息和学生状态

## 功能特性

### 1. 查询逻辑
- 查询学生参与的所有考试
- 只返回状态为 `'active'` 的考试
- 通过 `exams` 表和 `exam_students` 表的关联查询
- 按考试开始时间倒序排列（最新的在前）

### 2. 数据库查询
```sql
SELECT DISTINCT e.*, es.status as student_status, es.last_active
FROM exams e
INNER JOIN exam_students es ON e.id = es.exam_id
WHERE es.student_id = %s 
AND e.status = 'active'
ORDER BY e.start_time DESC
```

### 3. 时间格式化
自动将所有时间字段格式化为字符串格式 `"YYYY-MM-DD HH:MM:SS"`：
- `start_time` - 考试开始时间
- `end_time` - 考试结束时间  
- `created_at` - 考试创建时间
- `last_active` - 学生最后活跃时间

## 返回数据结构

```python
[
    {
        'id': 1,                                    # 考试ID
        'name': '期末考试',                          # 考试名称
        'start_time': '2024-01-15 09:00:00',       # 开始时间
        'end_time': '2024-01-15 11:00:00',         # 结束时间
        'status': 'active',                        # 考试状态
        'student_status': 'online',                # 学生在该考试中的状态
        'last_active': '2024-01-15 10:30:00',     # 学生最后活跃时间
        'default_url': 'http://exam.example.com', # 默认考试URL
        'delay_min': 5,                           # 延迟分钟数
        'disable_new_tabs': True,                 # 是否禁用新标签页
        'created_at': '2024-01-10 14:30:00'      # 考试创建时间
    }
]
```

## 使用示例

### 基本用法
```python
from data_access import DataAccess

data_access = DataAccess()

# 获取学生的活跃考试
student_id = "2023001"
active_exams = data_access.get_student_active_exams(student_id)

print(f"学生 {student_id} 有 {len(active_exams)} 个活跃考试")

for exam in active_exams:
    print(f"考试: {exam['name']}")
    print(f"状态: {exam['status']}")
    print(f"学生状态: {exam['student_status']}")
    print(f"时间: {exam['start_time']} - {exam['end_time']}")
```

### 检查学生是否有可参加的考试
```python
def check_student_can_take_exam(student_id):
    data_access = DataAccess()
    active_exams = data_access.get_student_active_exams(student_id)
    
    if not active_exams:
        print(f"学生 {student_id} 当前没有可参加的考试")
        return False
    
    print(f"学生 {student_id} 可以参加以下考试:")
    for exam in active_exams:
        print(f"- {exam['name']} ({exam['start_time']} - {exam['end_time']})")
    
    return True
```

### 获取学生当前考试状态
```python
def get_student_current_exam_status(student_id):
    data_access = DataAccess()
    active_exams = data_access.get_student_active_exams(student_id)
    
    for exam in active_exams:
        status = exam['student_status']
        last_active = exam.get('last_active', '无')
        
        print(f"考试: {exam['name']}")
        print(f"学生状态: {status}")
        print(f"最后活跃: {last_active}")
        
        if status == 'offline':
            print("⚠️  警告: 学生当前离线")
        elif status == 'online':
            print("✅ 学生当前在线")
```

## 错误处理

### 1. 学生不存在
如果学生ID不存在，函数返回空列表，不会抛出异常：
```python
active_exams = data_access.get_student_active_exams("不存在的学号")
# 返回: []
```

### 2. 数据库连接错误
```python
try:
    active_exams = data_access.get_student_active_exams(student_id)
except Exception as e:
    print(f"查询失败: {e}")
    active_exams = []
```

### 3. 时间格式化错误
如果时间字段格式异常，会自动转换为字符串：
```python
# 安全的时间格式化
if hasattr(exam['start_time'], 'strftime'):
    exam['start_time'] = exam['start_time'].strftime("%Y-%m-%d %H:%M:%S")
else:
    exam['start_time'] = str(exam['start_time'])
```

## 性能考虑

### 1. 数据库索引
确保以下字段有适当的索引：
- `exam_students.student_id`
- `exam_students.exam_id`
- `exams.status`
- `exams.start_time`

### 2. 查询优化
- 使用 `DISTINCT` 避免重复记录
- 使用 `INNER JOIN` 确保数据一致性
- 按 `start_time DESC` 排序，最新考试在前
- 只查询状态为 `'active'` 的考试

## 应用场景

### 1. 学生登录时显示可用考试
```python
def show_available_exams_on_login(student_id):
    active_exams = data_access.get_student_active_exams(student_id)
    
    if not active_exams:
        return "当前没有可参加的考试"
    
    exam_list = []
    for exam in active_exams:
        exam_list.append({
            'id': exam['id'],
            'name': exam['name'],
            'start_time': exam['start_time'],
            'end_time': exam['end_time']
        })
    
    return exam_list
```

### 2. 考试监控系统
```python
def monitor_student_exams(student_id):
    active_exams = data_access.get_student_active_exams(student_id)
    
    for exam in active_exams:
        if exam['student_status'] == 'offline':
            # 发送警告通知
            send_offline_warning(student_id, exam['id'])
```

### 3. 考试选择界面
```python
def create_exam_selection_ui(student_id):
    active_exams = data_access.get_student_active_exams(student_id)
    
    if len(active_exams) == 1:
        # 只有一个考试，直接进入
        return active_exams[0]
    elif len(active_exams) > 1:
        # 多个考试，显示选择界面
        return show_exam_selection_dialog(active_exams)
    else:
        # 没有考试
        return None
```

## 测试

运行测试脚本验证功能：
```bash
python test_get_student_active_exams.py
```

测试内容包括：
- ✅ 函数存在性检查
- ✅ 函数可调用性检查
- ✅ 不同学号的查询测试
- ✅ 返回数据结构验证
- ✅ 时间格式化验证
- ✅ 错误处理验证

## 总结

`get_student_active_exams` 函数提供了一个简单而强大的接口来查询学生的活跃考试：

### ✅ 核心功能:
- 根据学号查询活跃考试
- 只返回正在进行的考试
- 包含学生状态信息
- 自动时间格式化

### ✅ 技术特性:
- 高效的数据库查询
- 完善的错误处理
- 连接池管理
- 数据格式标准化

### ✅ 应用价值:
- 学生登录时显示可用考试
- 考试监控和状态管理
- 考试选择界面支持
- 系统集成友好

这个函数为考试系统提供了核心的学生考试查询能力！
