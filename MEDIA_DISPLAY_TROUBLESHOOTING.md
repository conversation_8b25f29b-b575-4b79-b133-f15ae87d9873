# 考试监控页面截图和录屏不显示问题排查

## 问题描述

考试监控页面点击"截图/录屏"按钮后，模态框显示但不显示任何截图或录屏内容。

## 已修复的问题

### 1. 前端错误处理缺失

**问题**: 原始代码没有错误处理，API调用失败时用户看不到任何提示。

**修复**: 添加了完整的错误处理和调试日志：

```javascript
// 修复前：没有错误处理
$.get(`/api/exams/${examId}/students/${studentId}/recordings`, function(data) {
    // 只有成功回调
});

// 修复后：完整的错误处理
$.get(`/api/exams/${examId}/students/${studentId}/recordings`)
    .done(function(data) {
        console.log('Recordings API response:', data);
        // 处理成功响应
    })
    .fail(function(xhr, status, error) {
        console.error('Recordings API error:', xhr.status, error);
        // 处理错误并显示给用户
    });
```

### 2. 加载动画不消失

**问题**: 渲染函数没有隐藏加载动画，导致一直显示"加载中..."。

**修复**: 在渲染函数中添加隐藏加载动画的代码：

```javascript
function renderScreenshots(screenshots) {
    // 渲染截图
    screenshots.forEach(s => {
        masonryContainer.append(`<div class="masonry-item"><img src="${s}" class="img-fluid"></div>`);
    });
    
    $('.loading-spinner').hide(); // 隐藏加载动画
}
```

### 3. 调试信息不足

**问题**: 无法知道API调用是否成功，返回了什么数据。

**修复**: 添加了详细的控制台日志：

```javascript
console.log(`Loading media for student: ${studentId}, exam: ${examId}`);
console.log('Recordings API response:', data);
console.log(`Found ${recordings.length} recordings`);
```

## 可能的根本原因

### 1. 数据库问题

**检查项目**:
- `screenshots` 表是否存在且有数据
- `exam_students` 表中是否有对应的学生记录
- 数据库连接是否正常

**检查方法**:
```sql
-- 检查截图表
SELECT * FROM screenshots WHERE exam_id = 1 AND student_id = '001';

-- 检查学生记录
SELECT * FROM exam_students WHERE exam_id = 1 AND student_id = '001';
```

### 2. 文件系统问题

**检查项目**:
- `server_data/{exam_id}/screenshots/` 目录是否存在
- `server_data/{exam_id}/recordings/` 目录是否存在
- 文件是否实际存在
- 文件权限是否正确

**检查方法**:
```bash
# 检查目录结构
ls -la server_data/1/
ls -la server_data/1/screenshots/
ls -la server_data/1/recordings/
```

### 3. API实现问题

**可能问题**:
- `data_access.get_student_screenshots()` 方法返回空结果
- `data_access.get_student_username()` 方法返回None
- 文件路径生成错误

### 4. 前端参数问题

**检查项目**:
- `examId` 和 `studentId` 参数是否正确传递
- 参数类型是否匹配（字符串 vs 数字）

## 诊断步骤

### 1. 运行诊断脚本

```bash
python diagnose_media_issue.py [服务器地址] [考试ID] [学生ID]
```

这个脚本会检查：
- 服务器状态
- API响应
- 文件系统状态
- 数据完整性

### 2. 浏览器开发者工具检查

1. 打开浏览器开发者工具 (F12)
2. 切换到 Network 标签
3. 点击"截图/录屏"按钮
4. 查看网络请求：
   - API请求是否发送
   - 响应状态码
   - 响应内容

5. 切换到 Console 标签查看日志：
   - 是否有JavaScript错误
   - API响应数据
   - 调试信息

### 3. 服务器日志检查

查看服务器控制台输出，检查：
- API请求是否到达服务器
- 是否有Python异常
- 数据库查询结果

### 4. 手动API测试

使用curl或Postman测试API：

```bash
# 测试截图API
curl "http://localhost:5000/api/exams/1/students/001/screenshots"

# 测试录屏API  
curl "http://localhost:5000/api/exams/1/students/001/recordings"

# 测试文件访问
curl "http://localhost:5000/screenshots/1/screenshot_001_1_2024-01-15_10-30-00.png"
```

## 常见解决方案

### 1. 数据库为空

**问题**: 数据库中没有截图或录屏记录

**解决**: 
- 确保学生客户端正在运行并上传截图
- 检查客户端是否能正常连接服务器
- 验证上传API是否正常工作

### 2. 文件路径错误

**问题**: 数据库中的路径与实际文件位置不匹配

**解决**:
- 检查`DATA_DIR`配置
- 确保文件保存路径与API读取路径一致
- 重新组织文件目录结构

### 3. 权限问题

**问题**: 服务器无法访问文件

**解决**:
```bash
# 设置正确的文件权限
chmod -R 755 server_data/
chown -R www-data:www-data server_data/  # 根据实际用户调整
```

### 4. API路径错误

**问题**: 前端调用的API路径不正确

**解决**: 确保API路径格式正确：
- 截图: `/api/exams/{exam_id}/students/{student_id}/screenshots`
- 录屏: `/api/exams/{exam_id}/students/{student_id}/recordings`

### 5. 参数类型不匹配

**问题**: exam_id或student_id类型不匹配

**解决**: 确保参数类型一致：
```javascript
// 确保exam_id是数字
const examId = parseInt(student.exam_id);
// 确保student_id是字符串
const studentId = String(student.student_id);
```

## 测试验证

### 1. 创建测试数据

如果没有真实数据，可以创建测试文件：

```bash
# 创建测试目录
mkdir -p server_data/1/screenshots
mkdir -p server_data/1/recordings

# 创建测试截图文件（空文件用于测试）
touch server_data/1/screenshots/screenshot_001_1_2024-01-15_10-30-00.png

# 创建测试录屏文件
touch server_data/1/recordings/recording_001_1_segment1.mp4
```

### 2. 数据库测试数据

```sql
-- 插入测试截图记录
INSERT INTO screenshots (exam_id, student_id, filename, timestamp) 
VALUES (1, '001', 'screenshot_001_1_2024-01-15_10-30-00.png', NOW());

-- 插入测试学生记录
INSERT INTO exam_students (exam_id, student_id, student_name, status) 
VALUES (1, '001', '测试学生', 'online');
```

## 总结

通过以上修复和诊断步骤，应该能够解决考试监控页面不显示截图和录屏的问题。关键是：

1. ✅ **前端错误处理**: 已添加完整的错误处理和调试信息
2. ✅ **加载状态管理**: 已修复加载动画不消失的问题
3. 🔍 **系统诊断**: 使用诊断脚本检查各个环节
4. 🔧 **逐步排查**: 从前端到后端，从API到文件系统

建议按照诊断步骤逐一检查，找出具体的问题所在。
