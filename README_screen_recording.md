# 录屏功能说明

## 概述

考试监控系统新增了录屏功能，可以实时录制学生屏幕并保存到服务器。该功能包括：

1. **定时录屏**：按设定的时间间隔自动录制屏幕
2. **单帧捕获**：捕获单帧屏幕截图
3. **视频上传**：将录制的视频文件上传到服务器
4. **数据管理**：服务器端管理录屏文件和元数据

## 功能特性

### 客户端功能
- 自动屏幕录制（可配置帧率和质量）
- 定时录制（可配置录制间隔）
- 单帧屏幕捕获
- 自动上传到服务器
- 资源清理和错误处理

### 服务器功能
- 接收录屏文件上传
- 接收单帧图像上传
- 录屏文件存储管理
- 录屏数据查询API
- 文件下载服务

## 安装依赖

在安装录屏功能前，需要安装以下依赖：

```bash
pip install mss==9.0.1 opencv-python==******** numpy==1.24.3
```

或者直接更新requirements.txt：

```bash
pip install -r requirements.txt
```

## 配置说明

### 客户端配置

录屏功能在客户端登录后自动启动，默认配置如下：

- **帧率**：10 FPS
- **质量**：80%
- **录制间隔**：30秒
- **临时目录**：`temp_videos/`

### 服务器配置

服务器端录屏文件存储：

- **录屏文件目录**：`server_data/screen_recordings/`
- **帧文件目录**：`server_data/screenshots/`（复用现有目录）

## API接口

### 1. 上传录屏文件

**POST** `/api/screen_recording`

**参数：**
- `video`：录屏文件（multipart/form-data）
- `student_id`：学生ID
- `exam_id`：考试ID
- `timestamp`：录制时间戳
- `fps`：帧率
- `quality`：质量

**响应：**
```json
{
    "status": "success",
    "message": "录屏已上传",
    "filename": "recording_123_1_20231201_143022.mp4",
    "file_size": 1024000
}
```

### 2. 上传单帧

**POST** `/api/screen_frame`

**参数：**
```json
{
    "student_id": "123",
    "exam_id": "1",
    "timestamp": "2023-12-01T14:30:22",
    "image_data": "base64编码的图像数据",
    "type": "single_frame"
}
```

**响应：**
```json
{
    "status": "success",
    "message": "帧已上传",
    "filename": "frame_123_1_20231201_143022.jpg"
}
```

### 3. 获取录屏列表

**GET** `/api/exams/{exam_id}/students/{student_id}/recordings`

**响应：**
```json
{
    "recordings": [
        {
            "filename": "recording_123_1_20231201_143022.mp4",
            "timestamp": "2023-12-01T14:30:22",
            "fps": 10,
            "quality": 80,
            "file_size": 1024000
        }
    ]
}
```

### 4. 获取帧列表

**GET** `/api/exams/{exam_id}/students/{student_id}/frames`

**响应：**
```json
{
    "frames": [
        {
            "filename": "frame_123_1_20231201_143022.jpg",
            "timestamp": "2023-12-01T14:30:22",
            "type": "frame"
        }
    ]
}
```

### 5. 下载录屏文件

**GET** `/screen_recordings/{filename}`

直接下载录屏文件。

## 使用方法

### 客户端使用

1. **自动启动**：客户端登录后自动启动录屏功能
2. **自动停止**：考试结束或客户端关闭时自动停止录屏
3. **手动控制**：可通过代码中的`ScreenRecorderManager`类进行手动控制

### 服务器使用

1. **启动服务器**：录屏API随主服务器自动启动
2. **查看录屏**：通过API获取录屏文件列表
3. **下载录屏**：通过文件下载API获取录屏文件

## 文件命名规则

### 录屏文件
```
recording_{student_id}_{exam_id}_{timestamp}.mp4
```

### 帧文件
```
frame_{student_id}_{exam_id}_{timestamp}.jpg
```

### 时间戳格式
```
YYYYMMDD_HHMMSS
```

## 性能考虑

### 客户端性能
- 录屏会占用一定的CPU和内存资源
- 建议在配置较低的机器上降低帧率和质量
- 临时文件会自动清理

### 服务器性能
- 录屏文件可能较大，注意磁盘空间
- 建议定期清理旧的录屏文件
- 网络带宽需求较高

## 故障排除

### 常见问题

1. **录屏启动失败**
   - 检查依赖是否正确安装
   - 检查临时目录权限
   - 查看客户端日志

2. **上传失败**
   - 检查网络连接
   - 检查服务器状态
   - 查看服务器日志

3. **文件损坏**
   - 检查磁盘空间
   - 检查文件权限
   - 重新录制

### 日志位置

- **客户端日志**：`logs/user_{username}_{date}.log`
- **服务器日志**：控制台输出

## 测试

运行测试脚本验证功能：

```bash
python test_screen_recording.py
```

## 注意事项

1. **隐私保护**：录屏功能会记录学生屏幕，请确保符合相关隐私法规
2. **存储空间**：录屏文件较大，注意服务器存储空间
3. **网络带宽**：上传录屏文件需要较多带宽
4. **性能影响**：录屏可能影响客户端性能，建议在测试环境验证

## 更新日志

- **v1.0**：初始版本，支持基本录屏功能
- 支持定时录制和单帧捕获
- 支持文件上传和下载
- 支持数据查询API 