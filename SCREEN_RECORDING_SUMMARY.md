# 录屏功能实现总结

## 概述

已成功为考试监控系统添加了完整的录屏功能，包括客户端录制、服务器存储和API管理。

## 新增文件

### 1. 核心模块
- **`screen_recorder.py`** - 录屏核心模块
  - `ScreenRecorder` 类：负责屏幕录制和视频编码
  - `ScreenRecorderManager` 类：管理录屏生命周期

### 2. 服务器端API
- **`server.py`** - 新增录屏相关API端点
  - `/api/screen_recording` - 接收录屏文件上传
  - `/api/screen_frame` - 接收单帧图像上传
  - `/screen_recordings/<filename>` - 提供录屏文件下载
  - `/api/exams/{exam_id}/students/{student_id}/recordings` - 获取录屏列表
  - `/api/exams/{exam_id}/students/{student_id}/frames` - 获取帧列表

### 3. 工具和测试
- **`test_screen_recording.py`** - API功能测试脚本
- **`screen_recording_manager.py`** - 录屏文件管理工具
- **`example_screen_recording.py`** - 使用示例
- **`install_screen_recording.py`** - 快速安装脚本

### 4. 文档
- **`README_screen_recording.md`** - 详细使用说明
- **`SCREEN_RECORDING_SUMMARY.md`** - 本总结文档

## 修改的文件

### 1. `requirements.txt`
- 添加录屏相关依赖：
  - `mss==9.0.1` - 屏幕捕获
  - `opencv-python==4.8.1.78` - 视频编码
  - `numpy==1.24.3` - 数值计算

### 2. `main.py` (客户端)
- 在 `on_login_success` 方法中初始化录屏管理器
- 在 `end_exam` 和 `on_close` 方法中停止录屏
- 录屏功能在客户端登录后自动启动

### 3. `server.py` (服务器)
- 添加录屏文件存储目录 `SCREEN_RECORDINGS_DIR`
- 新增5个录屏相关API端点
- 支持录屏文件和单帧图像的上传、存储和查询

## 功能特性

### 客户端功能
1. **自动录屏**：登录后自动启动，考试结束时自动停止
2. **可配置参数**：
   - 帧率：默认10 FPS，可调整
   - 质量：默认80%，可调整
   - 录制间隔：默认30秒
3. **单帧捕获**：支持捕获单帧屏幕截图
4. **自动上传**：录制的视频自动上传到服务器
5. **资源管理**：自动清理临时文件

### 服务器功能
1. **文件接收**：接收客户端上传的录屏文件和单帧图像
2. **数据存储**：将文件保存到指定目录
3. **元数据管理**：在Redis中记录文件信息
4. **API查询**：提供录屏文件列表查询
5. **文件下载**：提供录屏文件下载服务

## 技术实现

### 屏幕捕获
- 使用 `mss` 库进行跨平台屏幕捕获
- 支持多显示器环境
- 高性能，低资源占用

### 视频编码
- 使用 `opencv-python` 进行视频编码
- 支持MP4格式输出
- 可配置编码参数

### 数据传输
- 录屏文件：multipart/form-data 上传
- 单帧图像：base64编码的JSON上传
- 支持大文件传输

### 存储管理
- 录屏文件：`server_data/screen_recordings/`
- 帧文件：`server_data/screenshots/`（复用现有目录）
- Redis存储文件元数据

## 使用方法

### 快速开始
1. **安装依赖**：
   ```bash
   python install_screen_recording.py
   ```

2. **启动服务器**：
   ```bash
   python server.py
   ```

3. **启动客户端**：
   ```bash
   python main.py
   ```

4. **录屏功能自动启动**：客户端登录后自动开始录屏

### 管理录屏文件
```bash
# 列出录屏文件
python screen_recording_manager.py --action list

# 下载录屏文件
python screen_recording_manager.py --action download --filename recording_123_1_20231201_143022.mp4

# 清理旧文件
python screen_recording_manager.py --action cleanup --days 7

# 查看存储信息
python screen_recording_manager.py --action info
```

### 测试功能
```bash
# 测试API功能
python test_screen_recording.py

# 运行使用示例
python example_screen_recording.py
```

## API接口

### 上传录屏文件
```http
POST /api/screen_recording
Content-Type: multipart/form-data

video: [录屏文件]
student_id: "123"
exam_id: "1"
timestamp: "2023-12-01T14:30:22"
fps: "10"
quality: "80"
```

### 上传单帧
```http
POST /api/screen_frame
Content-Type: application/json

{
    "student_id": "123",
    "exam_id": "1",
    "timestamp": "2023-12-01T14:30:22",
    "image_data": "base64编码的图像数据",
    "type": "single_frame"
}
```

### 获取录屏列表
```http
GET /api/exams/1/students/123/recordings
```

### 下载录屏文件
```http
GET /screen_recordings/recording_123_1_20231201_143022.mp4
```

## 性能考虑

### 客户端性能
- 录屏会占用CPU和内存资源
- 建议在低配置机器上降低帧率和质量
- 临时文件自动清理，不占用磁盘空间

### 服务器性能
- 录屏文件较大，注意磁盘空间
- 网络带宽需求较高
- 建议定期清理旧文件

### 优化建议
1. **降低帧率**：从10 FPS降到5 FPS可减少50%的文件大小
2. **降低质量**：从80%降到60%可减少30%的文件大小
3. **增加录制间隔**：从30秒增加到60秒可减少50%的文件数量
4. **定期清理**：建议每天清理7天前的文件

## 安全考虑

1. **隐私保护**：录屏功能会记录学生屏幕，确保符合隐私法规
2. **文件权限**：录屏文件存储在服务器上，注意文件权限设置
3. **网络安全**：录屏数据通过HTTP传输，生产环境建议使用HTTPS

## 故障排除

### 常见问题
1. **录屏启动失败**：检查依赖是否正确安装
2. **上传失败**：检查网络连接和服务器状态
3. **文件损坏**：检查磁盘空间和文件权限

### 日志位置
- 客户端：`logs/user_{username}_{date}.log`
- 服务器：控制台输出

## 扩展功能

### 可能的改进
1. **实时流媒体**：支持WebSocket实时传输
2. **视频压缩**：添加更多编码格式支持
3. **智能录制**：只在检测到异常时录制
4. **云端存储**：支持云存储服务
5. **视频分析**：添加视频内容分析功能

## 总结

录屏功能已完整实现并集成到考试监控系统中。该功能提供了：

- ✅ 完整的客户端录屏功能
- ✅ 服务器端文件接收和存储
- ✅ 完整的API接口
- ✅ 文件管理和清理工具
- ✅ 详细的文档和示例
- ✅ 错误处理和日志记录

录屏功能现在可以投入使用，为考试监控提供更全面的监督能力。 