# ScreenRecorderManager 服务器配置集成

## 概述

已将 ScreenRecorderManager 修改为从服务器获取配置参数，而不是依赖本地配置文件。这样可以实现集中化的配置管理，管理员可以在服务器端统一调整所有客户端的录屏参数。

## 主要变更

### 1. ScreenRecorderManager 类修改

**文件**: `screen_recorder.py`

**主要变更**:
- 构造函数新增 `api_client` 参数
- 添加 `_load_config_from_server()` 方法从服务器获取配置
- 添加 `_load_default_config()` 方法提供默认配置
- 添加 `refresh_config_from_server()` 方法支持运行时刷新配置

**新的构造函数**:
```python
def __init__(self, server_url, student_id, exam_id, api_client=None):
```

### 2. BrowserCompatibleRecorderManager 类修改

**文件**: `browser_compatible_recorder.py`

**主要变更**:
- 与 ScreenRecorderManager 相同的配置加载机制
- 支持从服务器获取配置
- 支持运行时配置刷新

### 3. 主程序修改

**文件**: `main.py`

**主要变更**:
- 移除对 `ScreenRecordingConfig` 的依赖
- 将 `api_client` 传递给 ScreenRecorderManager
- 简化录屏管理器初始化代码

**修改前**:
```python
from screen_recording_config import ScreenRecordingConfig
screen_config = ScreenRecordingConfig()
upload_config = screen_config.get_upload_config()
self.screen_recorder = ScreenRecorderManager(server_url, student_id, exam_id)
self.screen_recorder.update_config(
    upload_strategy=upload_config.get("strategy", "distributed"),
    upload_interval=upload_config.get("interval", 120),
    max_upload_delay=upload_config.get("max_delay", 60)
)
```

**修改后**:
```python
self.screen_recorder = ScreenRecorderManager(
    server_url, 
    student_id, 
    exam_id,
    self.api_client  # 传递API客户端以从服务器获取配置
)
```

### 4. 服务器配置更新

**文件**: `config.json`

**新增配置段**:
```json
{
  "screen_recording": {
    "recording": {
      "enabled": true,
      "fps": 10,
      "quality": 80,
      "interval": 30
    },
    "upload": {
      "strategy": "distributed",
      "interval": 120,
      "max_delay": 60,
      "batch_size": 5,
      "retry_count": 3,
      "timeout": 60
    },
    "storage": {
      "temp_dir": "temp_videos",
      "max_temp_size": 1024,
      "cleanup_interval": 3600
    },
    "performance": {
      "max_concurrent_uploads": 3,
      "upload_delay": 1,
      "memory_limit": 512
    },
    "network": {
      "chunk_size": 8192,
      "connection_timeout": 30,
      "read_timeout": 60
    }
  }
}
```

## 配置加载机制

### 1. 优先级顺序

1. **服务器配置** (最高优先级)
   - 通过 API 客户端从服务器获取
   - 包含完整的录屏配置参数

2. **默认配置** (最低优先级)
   - 当无法从服务器获取配置时使用
   - 提供基本的录屏功能

### 2. 配置加载流程

```python
def _load_config_from_server(self):
    """从服务器加载配置"""
    try:
        if self.api_client:
            # 尝试从服务器获取配置
            success, server_config, error_msg = self.api_client.fetch_config()
            if success and server_config:
                # 从服务器配置中提取录屏相关配置
                screen_config = server_config.get("screen_recording", {})
                
                # 录制配置
                recording_config = screen_config.get("recording", {})
                self.recording_enabled = recording_config.get("enabled", True)
                self.recording_fps = recording_config.get("fps", 10)
                self.recording_quality = recording_config.get("quality", 80)
                self.recording_interval = recording_config.get("interval", 30)
                
                # 上传配置
                upload_config = screen_config.get("upload", {})
                self.upload_strategy = upload_config.get("strategy", "distributed")
                self.upload_interval = upload_config.get("interval", 120)
                self.max_upload_delay = upload_config.get("max_delay", 60)
                
                self.logger.info("已从服务器获取录屏配置")
                return
        
        # 如果无法从服务器获取配置，使用默认配置
        self._load_default_config()
        self.logger.info("使用默认录屏配置")
        
    except Exception as e:
        self.logger.error(f"加载服务器配置失败: {e}")
        self._load_default_config()
        self.logger.info("使用默认录屏配置")
```

## 运行时配置刷新

### 1. 刷新方法

```python
def refresh_config_from_server(self):
    """从服务器刷新配置"""
    # 重新从服务器获取配置
    # 如果正在录制，重启录制器以应用新配置
```

### 2. 使用场景

- 考试过程中需要调整录屏参数
- 服务器配置更新后需要立即生效
- 动态调整录制质量或频率

## 配置参数说明

### 录制配置 (recording)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| enabled | boolean | true | 是否启用录屏功能 |
| fps | integer | 10 | 录制帧率 (1-60) |
| quality | integer | 80 | 视频质量 (1-100) |
| interval | integer | 30 | 录制间隔（秒） |

### 上传配置 (upload)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| strategy | string | "distributed" | 上传策略 ("distributed" 或 "end_only") |
| interval | integer | 120 | 上传间隔（秒） |
| max_delay | integer | 60 | 最大随机延迟（秒） |
| batch_size | integer | 5 | 批量上传大小 |
| retry_count | integer | 3 | 重试次数 |
| timeout | integer | 60 | 上传超时时间（秒） |

### 存储配置 (storage)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| temp_dir | string | "temp_videos" | 临时目录 |
| max_temp_size | integer | 1024 | 最大临时文件大小（MB） |
| cleanup_interval | integer | 3600 | 清理间隔（秒） |

### 性能配置 (performance)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| max_concurrent_uploads | integer | 3 | 最大并发上传数 |
| upload_delay | integer | 1 | 上传间隔（秒） |
| memory_limit | integer | 512 | 内存限制（MB） |

### 网络配置 (network)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| chunk_size | integer | 8192 | 分块大小 |
| connection_timeout | integer | 30 | 连接超时 |
| read_timeout | integer | 60 | 读取超时 |

## 测试验证

### 1. 运行测试脚本

```bash
python test_server_config.py
```

### 2. 测试内容

- 服务器配置获取
- ScreenRecorderManager 配置加载
- BrowserCompatibleRecorderManager 配置加载
- 配置刷新功能

### 3. 预期结果

- ✅ 成功从服务器获取配置
- ✅ ScreenRecorderManager 创建成功
- ✅ 配置参数正确加载
- ✅ 配置刷新功能正常

## 兼容性说明

### 1. 向后兼容

- 如果服务器配置中缺少 `screen_recording` 段，会使用默认配置
- 如果无法连接到服务器，会使用默认配置
- 现有的本地配置文件仍然可以正常工作

### 2. 错误处理

- 网络连接失败时自动降级到默认配置
- 配置解析错误时记录日志并使用默认值
- 运行时配置刷新失败不影响当前录制

## 部署说明

### 1. 服务器端

1. 更新 `config.json` 文件，添加 `screen_recording` 配置段
2. 重启服务器以加载新配置
3. 验证 `/api/config` 接口返回正确的配置

### 2. 客户端端

1. 更新客户端代码
2. 确保 API 客户端正常工作
3. 测试录屏功能是否正常启动

### 3. 配置管理

- 管理员可以通过修改服务器 `config.json` 来调整所有客户端的录屏参数
- 配置更改会在客户端下次启动时生效
- 支持运行时配置刷新（需要客户端实现）

## 优势

1. **集中化管理**: 所有客户端配置统一在服务器端管理
2. **动态调整**: 支持运行时调整录屏参数
3. **容错性**: 网络问题时自动降级到默认配置
4. **可扩展性**: 易于添加新的配置参数
5. **监控友好**: 可以监控配置加载状态和错误

## 注意事项

1. 确保服务器 `/api/config` 接口正常工作
2. 网络连接不稳定时可能影响配置加载
3. 配置更改需要客户端重启或手动刷新才能生效
4. 建议在生产环境中添加配置验证机制 