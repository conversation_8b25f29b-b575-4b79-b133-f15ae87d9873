# 学生媒体文件API更新

## 更新概述

根据最新的基于exam_id的API结构，更新了学生截图、违规记录和录屏视频的API实现，确保所有媒体文件URL都使用正确的exam_id路径格式。

## API端点总览

### 1. 学生截图API
```
GET /api/exams/{exam_id}/students/{student_id}/screenshots
```

**功能**: 获取指定学生在指定考试中的所有截图
**支持分页**: 是 (page, per_page参数)
**返回格式**:
```json
{
  "screenshots": [
    "/screenshots/{exam_id}/screenshot_001_1_2024-01-15_10-30-00.png",
    "/screenshots/{exam_id}/screenshot_001_1_2024-01-15_10-31-00.png"
  ]
}
```

### 2. 学生录屏API
```
GET /api/exams/{exam_id}/students/{student_id}/recordings
```

**功能**: 获取指定学生在指定考试中的所有录屏文件（片段和合并后的）
**返回格式**:
```json
{
  "recordings": [
    {
      "filename": "recording_001_1_segment1.mp4",
      "type": "segment",
      "file_size": 1024000,
      "created_time": "2024-01-15T10:30:00",
      "download_url": "/recordings/{exam_id}/recording_001_1_segment1.mp4"
    },
    {
      "filename": "student_001_1_merged.mp4", 
      "type": "merged",
      "file_size": 5120000,
      "created_time": "2024-01-15T11:00:00",
      "download_url": "/recordings/{exam_id}/student_001_1_merged.mp4"
    }
  ]
}
```

### 3. 考试违规记录API
```
GET /api/exams/{exam_id}/violations
```

**功能**: 获取指定考试的所有违规记录
**支持分页**: 是 (page, per_page参数)
**返回格式**:
```json
[
  {
    "id": 1,
    "student_id": "001",
    "username": "张三",
    "reason": "切换窗口",
    "timestamp": "2024-01-15 10:30:00",
    "screenshot_url": "/screenshots/{exam_id}/violation_001_1_2024-01-15_10-30-00.png",
    "ip": "*************"
  }
]
```

## 文件服务端点

### 1. 截图文件服务
```
GET /screenshots/{exam_id}/{filename}
```

**功能**: 提供截图文件下载
**支持格式**: PNG, JPG, JPEG
**错误处理**: 考试存在性验证、文件存在性检查

### 2. 录屏文件服务
```
GET /recordings/{exam_id}/{filename}
```

**功能**: 提供录屏文件下载
**支持格式**: MP4, WEBM, AVI, MOV, MKV
**错误处理**: 考试存在性验证、文件存在性检查

## 服务器端修改

### 1. 截图API修复 (`server.py`)

**修改前**:
```python
urls = [f"/screenshots/{fname}" for fname in files]
```

**修改后**:
```python
# 生成正确的URL格式: /screenshots/{exam_id}/{filename}
urls = [f"/screenshots/{exam_id}/{fname}" for fname in files]
```

### 2. 录屏API修复 (`server.py`)

**修改前**:
```python
'download_url': f"/recordings/{filename}"
```

**修改后**:
```python
'download_url': f"/recordings/{exam_id}/{filename}"
```

### 3. 违规记录API增强 (`data_access.py`)

**已实现功能**:
- 自动生成正确的截图URL格式
- 时间戳格式化
- 文件名提取处理

```python
# 为每个违规记录生成正确的截图URL
for violation in violations:
    if violation.get('screenshot_path'):
        filename = os.path.basename(violation['screenshot_path'])
        violation['screenshot_url'] = f"/screenshots/{exam_id}/{filename}"
    else:
        violation['screenshot_url'] = None
```

## 前端兼容性

### index.html 已兼容

前端代码已经使用正确的API端点和URL处理：

```javascript
// 1. 获取学生录屏
$.get(`/api/exams/${examId}/students/${studentId}/recordings`, function(data) {
    let recordings = data.recordings || [];
    if (recordings.length > 0) {
        renderRecordings(recordings);
    } else {
        // 如果没有录屏，尝试获取截图
        $.get(`/api/exams/${examId}/students/${studentId}/screenshots`, function(data) {
            let screenshots = data.screenshots || [];
            if (screenshots.length > 0) {
                renderScreenshots(screenshots);
            }
        });
    }
});

// 2. 获取违规记录
$.ajax({
    url: `/api/exams/${currentExamId}/violations`,
    method: 'GET',
    data: {
        page: 1,
        per_page: VIOLATIONS_PER_PAGE
    },
    success: function(violations) {
        renderViolations(violations);
    }
});

// 3. 渲染截图
function renderScreenshots(screenshots) {
    screenshots.forEach(s => {
        // s 已经是完整的URL: /screenshots/{exam_id}/{filename}
        masonryContainer.append(`<div class="masonry-item"><img src="${s}" class="img-fluid"></div>`);
    });
}

// 4. 渲染录屏
function renderRecordings(recordings) {
    recordings.forEach(r => {
        // r.download_url 已经是完整的URL: /recordings/{exam_id}/{filename}
        masonryContainer.append(`
            <div class="masonry-item">
                <video controls width="100%" src="${r.download_url}"></video>
                <a href="${r.download_url}" target="_blank">下载</a>
            </div>
        `);
    });
}

// 5. 渲染违规记录
function renderViolations(violations) {
    violations.forEach(v => {
        // v.screenshot_url 已经是完整的URL: /screenshots/{exam_id}/{filename}
        const item = $(`
            <img src="${v.screenshot_url}" alt="违规截图" 
                onclick="showBigScreenshot('${v.screenshot_url}')"
                loading="lazy">
        `);
    });
}
```

## 文件目录结构

### 服务器端文件组织

```
server_data/
├── 1/                          # 考试ID为1
│   ├── screenshots/            # 截图文件
│   │   ├── screenshot_001_1_2024-01-15_10-30-00.png
│   │   ├── violation_001_1_2024-01-15_10-30-00.png
│   │   └── ...
│   └── recordings/             # 录屏文件
│       ├── recording_001_1_segment1.mp4
│       ├── recording_001_1_segment2.mp4
│       ├── student_001_1_merged.mp4
│       └── ...
├── 2/                          # 考试ID为2
│   ├── screenshots/
│   └── recordings/
└── ...
```

## 错误处理改进

### 1. 文件访问验证
- 验证考试是否存在
- 检查文件目录是否存在
- 验证文件是否存在
- 返回具体的错误信息

### 2. API响应格式统一
- 成功响应包含完整的文件信息
- 错误响应包含详细的错误描述
- 支持分页参数和元数据

### 3. 前端错误处理
- 优雅处理API调用失败
- 显示友好的错误提示
- 支持重试机制

## 性能优化

### 1. 分页支持
- 截图API支持分页加载
- 违规记录API支持分页加载
- 减少单次请求的数据量

### 2. 懒加载
- 图片使用lazy loading
- 视频按需加载
- 优化页面加载速度

### 3. 缓存策略
- 静态文件缓存
- API响应缓存
- 减少重复请求

## 测试验证

### 运行测试脚本
```bash
python test_student_media_apis.py [服务器地址] [考试ID] [学生ID]
```

### 测试内容
- ✅ 学生截图API功能和URL格式
- ✅ 学生录屏API功能和URL格式
- ✅ 违规记录API功能和URL格式
- ✅ 文件访问权限和存在性
- ✅ 错误处理和异常情况

## 总结

通过这次更新，实现了：

### ✅ 核心改进:
- 统一的基于exam_id的URL结构
- 正确的文件路径生成
- 完整的错误处理机制
- 前后端API一致性

### ✅ API优化:
- 学生截图: `/api/exams/{exam_id}/students/{student_id}/screenshots`
- 学生录屏: `/api/exams/{exam_id}/students/{student_id}/recordings`
- 违规记录: `/api/exams/{exam_id}/violations`
- 文件服务: `/screenshots/{exam_id}/{filename}`, `/recordings/{exam_id}/{filename}`

### ✅ 系统优势:
- 清晰的文件组织结构
- 易于维护和扩展
- 更好的安全性验证
- 完整的媒体文件管理

这个更新为考试监控系统提供了完整、一致、可靠的媒体文件API架构！
