# 录屏文件合并功能指南

## 录屏片段时长说明

### 当前录屏片段设置

根据代码分析，当前的录屏片段时长设置如下：

| 参数 | 值 | 说明 |
|------|----|----|
| **录制间隔** | 30秒 | 每30秒录制一个新的视频片段 |
| **上传间隔** | 120秒 | 每2分钟上传一次（包含多个30秒片段） |
| **实际片段时长** | 约30秒 | 每个录屏片段的实际时长 |

### 2小时考试的文件数量计算

```
总录制时间: 7200秒 (2小时)
录制间隔: 30秒
片段数量: 7200 ÷ 30 = 240个片段
每个片段: 约30秒
总文件数: 240个MP4文件
```

### 文件命名规则

```
recording_{student_id}_{exam_id}_{timestamp}.mp4
```

示例：
```
recording_123_1_20231201_143022.mp4
recording_123_1_20231201_143052.mp4
recording_123_1_20231201_143122.mp4
...
```

## 录屏文件合并功能

### 功能概述

录屏文件合并功能可以将学生的多个30秒录屏片段合并为一个完整的录屏文件，方便查看和管理。

### 合并优势

1. **文件管理简化**：从240个片段合并为1个完整文件
2. **查看便利**：可以连续播放整个考试过程
3. **存储优化**：减少文件数量，便于归档
4. **下载方便**：只需下载一个文件而不是多个片段

### 技术实现

#### 合并算法
- **时间顺序合并**：按录制时间顺序合并片段
- **帧率保持**：保持原始帧率（默认10 FPS）
- **质量保持**：保持原始视频质量
- **格式统一**：输出为MP4格式

#### 合并过程
1. 获取学生的所有录屏片段
2. 按时间戳排序
3. 逐个读取视频片段
4. 按顺序写入合并文件
5. 生成完整的录屏文件

## 使用方法

### 1. 合并单个学生的录屏文件

```bash
# 合并指定学生的录屏文件
python video_merger.py --action merge_student --exam-id 1 --student-id 123 --student-name "张三"
```

### 2. 合并所有学生的录屏文件

```bash
# 合并考试中所有学生的录屏文件
python video_merger.py --action merge_all --exam-id 1
```

### 3. 检查合并状态

```bash
# 查看合并状态
python video_merger.py --action status --exam-id 1
```

### 4. 清理临时文件

```bash
# 清理原始录屏文件（保留合并后的文件）
python video_merger.py --action cleanup --exam-id 1 --keep-merged
```

## API接口

### 1. 获取合并后的录屏文件列表

```http
GET /api/exams/{exam_id}/merged_recordings
```

**响应示例：**
```json
{
  "merged_recordings": [
    {
      "filename": "merged_张三_1_20231201_150000.mp4",
      "student_name": "张三",
      "exam_id": "1",
      "file_size": 1048576000,
      "created_time": "2023-12-01T15:00:00",
      "download_url": "/merged_recordings/merged_张三_1_20231201_150000.mp4"
    }
  ]
}
```

### 2. 下载合并后的录屏文件

```http
GET /merged_recordings/{filename}
```

## 文件结构

### 原始录屏文件
```
server_data/screen_recordings/
├── recording_123_1_20231201_143022.mp4  (30秒)
├── recording_123_1_20231201_143052.mp4  (30秒)
├── recording_123_1_20231201_143122.mp4  (30秒)
└── ... (240个文件)
```

### 合并后的录屏文件
```
server_data/merged_recordings/
├── merged_张三_1_20231201_150000.mp4    (2小时完整录屏)
├── merged_李四_1_20231201_150100.mp4    (2小时完整录屏)
└── merge_info_123_1_20231201_150000.json (合并信息)
```

## 性能考虑

### 合并时间估算

| 学生数量 | 每个学生片段数 | 预计合并时间 |
|----------|----------------|--------------|
| 1名学生 | 240个片段 | 2-3分钟 |
| 10名学生 | 2400个片段 | 20-30分钟 |
| 100名学生 | 24000个片段 | 3-4小时 |
| 400名学生 | 96000个片段 | 12-16小时 |

### 优化建议

1. **分批处理**：大量学生时建议分批合并
2. **并发处理**：使用多线程并发合并
3. **资源监控**：监控CPU和内存使用
4. **磁盘空间**：确保有足够空间存储合并文件

## 使用示例

### Python代码示例

```python
from video_merger import VideoMerger

# 创建合并器
merger = VideoMerger("http://localhost:5000")

# 合并单个学生
result = merger.merge_student_recordings("1", "123", "张三")
if result:
    print(f"合并成功: {result}")

# 合并所有学生
merger.merge_all_students("1")

# 检查状态
status = merger.get_merge_status("1")
for info in status:
    print(f"学生: {info['student_name']}, 文件: {info['merged_file']}")
```

### 命令行示例

```bash
# 1. 检查录屏文件
python video_merger.py --action status --exam-id 1

# 2. 合并单个学生
python video_merger.py --action merge_student --exam-id 1 --student-id 123 --student-name "张三"

# 3. 合并所有学生（建议分批进行）
python video_merger.py --action merge_all --exam-id 1

# 4. 下载合并后的文件
curl http://localhost:5000/merged_recordings/merged_张三_1_20231201_150000.mp4 -o 张三_完整录屏.mp4
```

## 故障排除

### 常见问题

1. **合并失败**
   - 检查原始录屏文件是否完整
   - 确认文件格式是否正确
   - 检查磁盘空间是否充足

2. **合并时间过长**
   - 减少并发数量
   - 分批处理学生
   - 优化服务器性能

3. **文件损坏**
   - 检查原始文件完整性
   - 重新下载损坏的文件
   - 验证合并后的文件

### 日志查看

```bash
# 查看合并日志
tail -f video_merger.log

# 查看详细错误信息
grep "ERROR" video_merger.log
```

## 最佳实践

### 1. 合并时机
- **考试结束后**：等所有录屏文件上传完成后再合并
- **分批处理**：大量学生时分批合并，避免服务器压力
- **定期合并**：可以设置定时任务定期合并

### 2. 存储管理
- **保留原始文件**：合并后保留原始文件作为备份
- **定期清理**：定期清理过期的原始文件
- **压缩存储**：可以考虑压缩合并后的文件

### 3. 性能优化
- **并发控制**：控制并发合并数量
- **资源监控**：监控CPU、内存、磁盘使用
- **网络优化**：确保网络带宽充足

## 总结

录屏文件合并功能完美解决了多个录屏片段管理的问题：

✅ **片段时长**：每个录屏片段约30秒
✅ **文件数量**：2小时考试产生约240个片段
✅ **合并功能**：支持将多个片段合并为完整录屏
✅ **管理便利**：从240个文件简化为1个文件
✅ **查看体验**：支持连续播放整个考试过程

通过合理使用合并功能，可以大大简化录屏文件的管理和查看体验。 