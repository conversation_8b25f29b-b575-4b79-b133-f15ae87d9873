#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查FFmpeg安装状态
"""

import subprocess
import shutil
import sys

def check_ffmpeg():
    """检查FFmpeg是否已安装"""
    print("检查FFmpeg安装状态...")
    
    # 方法1: 使用shutil.which检查
    ffmpeg_path = shutil.which('ffmpeg')
    if ffmpeg_path:
        print(f"✓ FFmpeg已安装: {ffmpeg_path}")
    else:
        print("✗ FFmpeg未安装或不在PATH中")
        return False
    
    # 方法2: 尝试运行ffmpeg -version
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            # 提取版本信息
            version_line = result.stdout.split('\n')[0]
            print(f"✓ FFmpeg版本: {version_line}")
            return True
        else:
            print(f"✗ FFmpeg运行失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("✗ FFmpeg运行超时")
        return False
    except Exception as e:
        print(f"✗ 检查FFmpeg时出错: {e}")
        return False

def install_ffmpeg_guide():
    """提供FFmpeg安装指南"""
    print("\n" + "="*50)
    print("FFmpeg安装指南")
    print("="*50)
    
    print("\nWindows安装方法:")
    print("1. 下载FFmpeg: https://ffmpeg.org/download.html")
    print("2. 解压到某个目录，如 C:\\ffmpeg")
    print("3. 将 C:\\ffmpeg\\bin 添加到系统PATH环境变量")
    print("4. 重启命令行或IDE")
    
    print("\n或者使用包管理器:")
    print("- Chocolatey: choco install ffmpeg")
    print("- Scoop: scoop install ffmpeg")
    print("- Winget: winget install ffmpeg")
    
    print("\n验证安装:")
    print("在命令行中运行: ffmpeg -version")

def main():
    """主函数"""
    print("FFmpeg安装状态检查")
    print("="*30)
    
    if check_ffmpeg():
        print("\n✓ FFmpeg已正确安装，可以用于视频格式转换")
        return True
    else:
        print("\n✗ FFmpeg未安装或配置不正确")
        install_ffmpeg_guide()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 