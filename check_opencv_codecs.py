#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测OpenCV支持的编码器
"""

import cv2
import numpy as np
import os

def test_codec_availability():
    """测试编码器可用性"""
    print("OpenCV编码器可用性检测")
    print("=" * 50)
    
    # 测试参数
    width, height = 640, 480
    fps = 10
    
    # 编码器列表
    codecs = [
        ('H264', '.mp4', 'H.264/AVC'),
        ('mp4v', '.mp4', 'MPEG-4 Part 2'),
        ('VP80', '.webm', 'VP8'),
        ('VP90', '.webm', 'VP9'),
        ('XVID', '.avi', 'XVID'),
        ('MJPG', '.avi', 'Motion JPEG'),
        ('DIVX', '.avi', 'DIVX'),
        ('mp4v', '.mp4', 'MP4V'),
        ('avc1', '.mp4', 'AVC1'),
        ('h264', '.mp4', 'H264'),
    ]
    
    results = []
    
    for codec, ext, description in codecs:
        print(f"\n测试编码器: {codec} ({description})")
        print("-" * 40)
        
        test_file = f"test_{codec}{ext}"
        
        try:
            # 测试FourCC创建
            fourcc = cv2.VideoWriter_fourcc(*codec)
            print(f"✓ FourCC创建成功: {fourcc}")
            
            # 测试VideoWriter创建
            writer = cv2.VideoWriter(test_file, fourcc, fps, (width, height))
            
            if writer.isOpened():
                print(f"✓ VideoWriter创建成功")
                
                # 写入测试帧
                test_frame = np.zeros((height, width, 3), dtype=np.uint8)
                test_frame[:, :, 0] = 128  # 蓝色
                test_frame[:, :, 1] = 128  # 绿色
                test_frame[:, :, 2] = 128  # 红色
                
                # 添加文字
                cv2.putText(test_frame, f"Test: {codec}", (50, 100), 
                          cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                
                writer.write(test_frame)
                writer.release()
                
                # 检查文件
                if os.path.exists(test_file):
                    file_size = os.path.getsize(test_file)
                    print(f"✓ 测试文件创建成功: {file_size:,} bytes")
                    
                    # 测试读取
                    cap = cv2.VideoCapture(test_file)
                    if cap.isOpened():
                        print(f"✓ 文件可读取")
                        cap.release()
                        results.append({
                            'codec': codec,
                            'extension': ext,
                            'description': description,
                            'status': 'fully_supported',
                            'file_size': file_size
                        })
                    else:
                        print("⚠ 文件创建成功但无法读取")
                        results.append({
                            'codec': codec,
                            'extension': ext,
                            'description': description,
                            'status': 'write_only',
                            'file_size': file_size
                        })
                else:
                    print("✗ 文件未创建")
                    results.append({
                        'codec': codec,
                        'extension': ext,
                        'description': description,
                        'status': 'failed'
                    })
            else:
                print("✗ VideoWriter创建失败")
                results.append({
                    'codec': codec,
                    'extension': ext,
                    'description': description,
                    'status': 'not_supported'
                })
                
        except Exception as e:
            print(f"✗ 测试失败: {e}")
            results.append({
                'codec': codec,
                'extension': ext,
                'description': description,
                'status': 'error',
                'error': str(e)
            })
        
        # 清理测试文件
        if os.path.exists(test_file):
            try:
                os.remove(test_file)
            except:
                pass
    
    return results

def print_summary(results):
    """打印总结"""
    print("\n" + "=" * 60)
    print("编码器支持情况总结")
    print("=" * 60)
    
    # 按状态分组
    fully_supported = [r for r in results if r['status'] == 'fully_supported']
    write_only = [r for r in results if r['status'] == 'write_only']
    not_supported = [r for r in results if r['status'] == 'not_supported']
    failed = [r for r in results if r['status'] == 'failed']
    errors = [r for r in results if r['status'] == 'error']
    
    print(f"\n✅ 完全支持 ({len(fully_supported)}个):")
    for r in fully_supported:
        print(f"  - {r['codec']}{r['extension']} ({r['description']}) - {r['file_size']:,} bytes")
    
    print(f"\n⚠️ 仅写入支持 ({len(write_only)}个):")
    for r in write_only:
        print(f"  - {r['codec']}{r['extension']} ({r['description']}) - {r['file_size']:,} bytes")
    
    print(f"\n❌ 不支持 ({len(not_supported)}个):")
    for r in not_supported:
        print(f"  - {r['codec']}{r['extension']} ({r['description']})")
    
    print(f"\n💥 测试失败 ({len(failed)}个):")
    for r in failed:
        print(f"  - {r['codec']}{r['extension']} ({r['description']})")
    
    print(f"\n🚨 错误 ({len(errors)}个):")
    for r in errors:
        print(f"  - {r['codec']}{r['extension']} ({r['description']}): {r.get('error', 'Unknown error')}")
    
    # 推荐编码器
    print(f"\n🎯 推荐编码器:")
    if fully_supported:
        print("  完全支持的编码器（按文件大小排序）:")
        sorted_supported = sorted(fully_supported, key=lambda x: x.get('file_size', 0))
        for r in sorted_supported[:3]:  # 显示前3个
            print(f"    - {r['codec']}{r['extension']} ({r['description']})")
    else:
        print("  没有完全支持的编码器，建议使用仅写入支持的编码器")

def check_opencv_version():
    """检查OpenCV版本"""
    print(f"OpenCV版本: {cv2.__version__}")
    print(f"OpenCV构建信息: {cv2.getBuildInformation()}")

def main():
    """主函数"""
    print("OpenCV编码器检测工具")
    print("=" * 30)
    
    # 检查OpenCV版本
    check_opencv_version()
    
    # 测试编码器
    results = test_codec_availability()
    
    # 打印总结
    print_summary(results)
    
    return results

if __name__ == "__main__":
    main() 