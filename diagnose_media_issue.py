#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
诊断考试监控页面截图和录屏不显示的问题
"""

import requests
import json
import sys
import os

def check_server_status(server_url):
    """检查服务器状态"""
    print("=== 检查服务器状态 ===")
    
    try:
        response = requests.get(f"{server_url}/api/exams", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器正常运行")
            exams = response.json()
            print(f"找到 {len(exams)} 个考试")
            return True, exams
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False, []
    except Exception as e:
        print(f"❌ 无法连接服务器: {e}")
        return False, []

def check_exam_students(server_url, exam_id):
    """检查考试学生"""
    print(f"\n=== 检查考试 {exam_id} 的学生 ===")
    
    try:
        response = requests.get(f"{server_url}/api/exams/{exam_id}/students", timeout=5)
        if response.status_code == 200:
            students = response.json()
            print(f"✅ 找到 {len(students)} 个学生")
            for student in students[:3]:  # 显示前3个学生
                print(f"  - 学生ID: {student.get('student_id')}, 姓名: {student.get('student_name')}")
            return True, students
        else:
            print(f"❌ 获取学生列表失败: {response.status_code}")
            return False, []
    except Exception as e:
        print(f"❌ 获取学生列表异常: {e}")
        return False, []

def check_screenshots_api(server_url, exam_id, student_id):
    """检查截图API"""
    print(f"\n=== 检查截图API ===")
    print(f"考试ID: {exam_id}, 学生ID: {student_id}")
    
    try:
        url = f"{server_url}/api/exams/{exam_id}/students/{student_id}/screenshots"
        print(f"请求URL: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 截图API调用成功")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            screenshots = data.get('screenshots', [])
            print(f"截图数量: {len(screenshots)}")
            
            # 测试第一个截图文件是否可访问
            if screenshots:
                first_screenshot = screenshots[0]
                print(f"测试第一个截图: {first_screenshot}")
                
                img_response = requests.get(f"{server_url}{first_screenshot}", timeout=5)
                print(f"截图文件状态码: {img_response.status_code}")
                if img_response.status_code == 200:
                    print("✅ 截图文件可访问")
                else:
                    print(f"❌ 截图文件不可访问: {img_response.text}")
            
            return True, screenshots
        else:
            print(f"❌ 截图API失败")
            print(f"响应内容: {response.text}")
            return False, []
            
    except Exception as e:
        print(f"❌ 截图API异常: {e}")
        return False, []

def check_recordings_api(server_url, exam_id, student_id):
    """检查录屏API"""
    print(f"\n=== 检查录屏API ===")
    print(f"考试ID: {exam_id}, 学生ID: {student_id}")
    
    try:
        url = f"{server_url}/api/exams/{exam_id}/students/{student_id}/recordings"
        print(f"请求URL: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 录屏API调用成功")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            recordings = data.get('recordings', [])
            print(f"录屏数量: {len(recordings)}")
            
            # 测试第一个录屏文件是否可访问
            if recordings:
                first_recording = recordings[0]
                download_url = first_recording.get('download_url')
                print(f"测试第一个录屏: {download_url}")
                
                video_response = requests.head(f"{server_url}{download_url}", timeout=5)
                print(f"录屏文件状态码: {video_response.status_code}")
                if video_response.status_code == 200:
                    print("✅ 录屏文件可访问")
                    print(f"Content-Type: {video_response.headers.get('Content-Type')}")
                else:
                    print(f"❌ 录屏文件不可访问")
            
            return True, recordings
        else:
            print(f"❌ 录屏API失败")
            print(f"响应内容: {response.text}")
            return False, []
            
    except Exception as e:
        print(f"❌ 录屏API异常: {e}")
        return False, []

def check_file_system(exam_id, student_id):
    """检查文件系统"""
    print(f"\n=== 检查文件系统 ===")
    
    # 检查数据目录
    data_dir = "server_data"  # 假设的数据目录
    exam_dir = os.path.join(data_dir, str(exam_id))
    screenshots_dir = os.path.join(exam_dir, "screenshots")
    recordings_dir = os.path.join(exam_dir, "recordings")
    
    print(f"检查目录: {exam_dir}")
    
    if os.path.exists(exam_dir):
        print(f"✅ 考试目录存在: {exam_dir}")
        
        if os.path.exists(screenshots_dir):
            print(f"✅ 截图目录存在: {screenshots_dir}")
            screenshot_files = [f for f in os.listdir(screenshots_dir) if f.endswith(('.png', '.jpg', '.jpeg'))]
            print(f"截图文件数量: {len(screenshot_files)}")
            
            # 显示相关的截图文件
            student_screenshots = [f for f in screenshot_files if student_id in f]
            print(f"学生 {student_id} 的截图: {len(student_screenshots)}")
            for f in student_screenshots[:3]:
                print(f"  - {f}")
        else:
            print(f"❌ 截图目录不存在: {screenshots_dir}")
        
        if os.path.exists(recordings_dir):
            print(f"✅ 录屏目录存在: {recordings_dir}")
            recording_files = [f for f in os.listdir(recordings_dir) if f.endswith(('.mp4', '.webm', '.avi'))]
            print(f"录屏文件数量: {len(recording_files)}")
            
            # 显示相关的录屏文件
            student_recordings = [f for f in recording_files if student_id in f]
            print(f"学生 {student_id} 的录屏: {len(student_recordings)}")
            for f in student_recordings[:3]:
                print(f"  - {f}")
        else:
            print(f"❌ 录屏目录不存在: {recordings_dir}")
    else:
        print(f"❌ 考试目录不存在: {exam_dir}")

def main():
    """主诊断函数"""
    print("考试监控页面媒体文件诊断")
    print("=" * 50)
    
    # 配置
    server_url = "http://127.0.0.1:5000"
    exam_id = None
    student_id = None
    
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    if len(sys.argv) > 2:
        exam_id = int(sys.argv[2])
    if len(sys.argv) > 3:
        student_id = sys.argv[3]
    
    print(f"服务器地址: {server_url}")
    
    # 1. 检查服务器状态
    server_ok, exams = check_server_status(server_url)
    if not server_ok:
        print("❌ 服务器不可用，诊断终止")
        return
    
    # 2. 获取测试数据
    if not exam_id and exams:
        exam_id = exams[0]['id']
        print(f"使用第一个考试: {exam_id}")
    
    if exam_id:
        students_ok, students = check_exam_students(server_url, exam_id)
        if students_ok and students and not student_id:
            student_id = students[0]['student_id']
            print(f"使用第一个学生: {student_id}")
    
    if not exam_id or not student_id:
        print("❌ 无法获取测试数据")
        return
    
    print(f"\n使用测试数据: 考试ID={exam_id}, 学生ID={student_id}")
    
    # 3. 检查API
    screenshots_ok, screenshots = check_screenshots_api(server_url, exam_id, student_id)
    recordings_ok, recordings = check_recordings_api(server_url, exam_id, student_id)
    
    # 4. 检查文件系统
    check_file_system(exam_id, student_id)
    
    # 5. 总结
    print("\n" + "=" * 50)
    print("=== 诊断总结 ===")
    
    if screenshots_ok:
        print(f"✅ 截图API正常 (找到 {len(screenshots)} 个截图)")
    else:
        print("❌ 截图API异常")
    
    if recordings_ok:
        print(f"✅ 录屏API正常 (找到 {len(recordings)} 个录屏)")
    else:
        print("❌ 录屏API异常")
    
    if not screenshots_ok and not recordings_ok:
        print("\n🔍 可能的问题:")
        print("1. 数据库中没有截图/录屏记录")
        print("2. 文件系统中没有对应的文件")
        print("3. API路径或参数错误")
        print("4. 权限问题")
        print("5. 服务器配置问题")
        
        print("\n💡 建议检查:")
        print("1. 确认学生是否已经上传过截图或录屏")
        print("2. 检查数据库中的screenshots和recordings表")
        print("3. 检查文件系统权限")
        print("4. 查看服务器日志")
        print("5. 在浏览器开发者工具中查看网络请求")

if __name__ == "__main__":
    main()
