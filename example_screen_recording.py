#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
录屏功能使用示例
演示如何使用录屏功能
"""

import time
import threading
from screen_recorder import ScreenRecorder, ScreenRecorderManager

def example_basic_recording():
    """基本录屏示例"""
    print("=== 基本录屏示例 ===")
    
    # 创建录屏器
    recorder = ScreenRecorder(
        server_url="http://localhost:5000",
        student_id="example_student_001",
        exam_id="1",
        fps=10,
        quality=80
    )
    
    try:
        # 开始录制
        print("开始录制...")
        if recorder.start_recording():
            print("录制已开始，等待5秒...")
            time.sleep(5)
            
            # 停止录制
            print("停止录制...")
            recorder.stop_recording()
            print("录制已停止")
        else:
            print("启动录制失败")
            
    except Exception as e:
        print(f"录屏出错: {e}")
    finally:
        # 清理资源
        recorder.cleanup()

def example_recording_manager():
    """录屏管理器示例"""
    print("\n=== 录屏管理器示例 ===")
    
    # 创建录屏管理器
    manager = ScreenRecorderManager(
        server_url="http://localhost:5000",
        student_id="example_student_002",
        exam_id="1"
    )
    
    try:
        # 启动管理器
        print("启动录屏管理器...")
        manager.start()
        
        # 运行一段时间
        print("录屏管理器运行中，等待10秒...")
        time.sleep(10)
        
        # 捕获单帧
        print("捕获单帧...")
        if manager.capture_frame():
            print("单帧捕获成功")
        else:
            print("单帧捕获失败")
        
        # 更新配置
        print("更新录屏配置...")
        manager.update_config(fps=15, quality=90, interval=20)
        
        # 继续运行
        print("继续运行5秒...")
        time.sleep(5)
        
        # 停止管理器
        print("停止录屏管理器...")
        manager.stop()
        print("录屏管理器已停止")
        
    except Exception as e:
        print(f"录屏管理器出错: {e}")

def example_custom_recording():
    """自定义录屏示例"""
    print("\n=== 自定义录屏示例 ===")
    
    # 创建录屏器（高帧率，高质量）
    recorder = ScreenRecorder(
        server_url="http://localhost:5000",
        student_id="example_student_003",
        exam_id="1",
        fps=20,  # 高帧率
        quality=95  # 高质量
    )
    
    try:
        # 开始录制
        print("开始高质量录制...")
        if recorder.start_recording():
            print("录制已开始，等待3秒...")
            time.sleep(3)
            
            # 停止录制
            print("停止录制...")
            recorder.stop_recording()
            print("录制已停止")
        else:
            print("启动录制失败")
            
    except Exception as e:
        print(f"自定义录屏出错: {e}")
    finally:
        recorder.cleanup()

def example_multiple_recordings():
    """多段录屏示例"""
    print("\n=== 多段录屏示例 ===")
    
    recorder = ScreenRecorder(
        server_url="http://localhost:5000",
        student_id="example_student_004",
        exam_id="1",
        fps=10,
        quality=80
    )
    
    try:
        # 第一段录制
        print("第一段录制开始...")
        recorder.start_recording()
        time.sleep(2)
        recorder.stop_recording()
        print("第一段录制结束")
        
        # 等待一下
        time.sleep(1)
        
        # 第二段录制
        print("第二段录制开始...")
        recorder.start_recording()
        time.sleep(2)
        recorder.stop_recording()
        print("第二段录制结束")
        
    except Exception as e:
        print(f"多段录屏出错: {e}")
    finally:
        recorder.cleanup()

def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    # 使用错误的服务器URL
    recorder = ScreenRecorder(
        server_url="http://invalid-server:9999",
        student_id="example_student_005",
        exam_id="1"
    )
    
    try:
        print("尝试使用错误服务器URL录制...")
        if recorder.start_recording():
            time.sleep(2)
            recorder.stop_recording()
        else:
            print("预期：启动录制失败（服务器不可达）")
            
    except Exception as e:
        print(f"预期错误: {e}")
    finally:
        recorder.cleanup()

def main():
    """主函数"""
    print("录屏功能使用示例")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        import requests
        response = requests.get("http://localhost:5000/api/server_time", timeout=5)
        if response.status_code == 200:
            print("✓ 服务器连接正常")
        else:
            print("✗ 服务器响应异常")
            return
    except Exception as e:
        print(f"✗ 无法连接到服务器: {e}")
        print("请确保服务器正在运行在 http://localhost:5000")
        return
    
    # 运行示例
    try:
        example_basic_recording()
        example_recording_manager()
        example_custom_recording()
        example_multiple_recordings()
        example_error_handling()
        
        print("\n" + "=" * 50)
        print("所有示例运行完成")
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"示例运行出错: {e}")

if __name__ == "__main__":
    main() 