# Gunicorn 配置文件
import multiprocessing

# 绑定的IP和端口
bind = "0.0.0.0:5000"

# 工作进程数 - 使用单进程避免MergeManager任务丢失
workers = 1  # 重要：使用单进程确保MergeManager正常工作

# 工作模式 - 使用同步模式确保稳定性
worker_class = "sync"

# 超时时间
timeout = 120

# 访问日志格式
accesslog = "logs/gunicorn_access.log"
errorlog = "logs/gunicorn_error.log"

# 进程名称
proc_name = "exam_monitor"

# 后台运行
daemon = False

# 预加载应用 - 确保单例模式正常工作
preload_app = True

# 工作进程超时时间
graceful_timeout = 120

# 最大客户端并发数量 - 每个worker支持更多连接
worker_connections = 1000

# 性能配置
keepalive = 2

# 日志级别
loglevel = "info"

# 回调函数
def when_ready(server):
    """服务器启动时的回调"""
    import os
    print(f"[Gunicorn] 服务器启动完成，PID: {os.getpid()}, Workers: {workers}")

def worker_int(worker):
    """Worker 进程中断时的回调"""
    print(f"[Gunicorn] Worker {worker.pid} 收到中断信号")

def on_exit(server):
    """服务器退出时的回调"""
    import os
    print(f"[Gunicorn] 服务器退出，PID: {os.getpid()}")

def post_fork(server, worker):
    """Worker 进程启动后的回调"""
    print(f"[Gunicorn] Worker {worker.pid} 启动完成")
