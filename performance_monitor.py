#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能监控脚本
监控服务器在高并发下的性能指标
"""

import time
import psutil
import redis
import requests
import json
from datetime import datetime
import threading

class PerformanceMonitor:
    def __init__(self, redis_host='localhost', redis_port=6379, server_url='http://localhost:5000'):
        self.redis_host = redis_host
        self.redis_port = redis_port
        self.server_url = server_url
        self.monitoring = False
        
    def get_system_metrics(self):
        """获取系统性能指标"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            'timestamp': datetime.now().isoformat(),
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'memory_available_gb': memory.available / (1024**3),
            'disk_percent': disk.percent,
            'disk_free_gb': disk.free / (1024**3)
        }
    
    def get_redis_metrics(self):
        """获取Redis性能指标"""
        try:
            client = redis.Redis(host=self.redis_host, port=self.redis_port)
            info = client.info()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'connected_clients': info.get('connected_clients', 0),
                'used_memory_human': info.get('used_memory_human', '0B'),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'uptime_in_seconds': info.get('uptime_in_seconds', 0)
            }
        except Exception as e:
            return {'error': str(e)}
    
    def get_server_metrics(self):
        """获取服务器API响应时间"""
        try:
            start_time = time.time()
            response = requests.get(f"{self.server_url}/api/server_time", timeout=5)
            response_time = (time.time() - start_time) * 1000  # 毫秒
            
            return {
                'timestamp': datetime.now().isoformat(),
                'response_time_ms': response_time,
                'status_code': response.status_code,
                'server_time': response.json().get('server_time', '')
            }
        except Exception as e:
            return {'error': str(e)}
    
    def monitor_continuously(self, interval=10):
        """持续监控性能指标"""
        self.monitoring = True
        
        while self.monitoring:
            try:
                # 获取各项指标
                system_metrics = self.get_system_metrics()
                redis_metrics = self.get_redis_metrics()
                server_metrics = self.get_server_metrics()
                
                # 输出监控结果
                print(f"\n=== 性能监控报告 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===")
                print(f"CPU使用率: {system_metrics['cpu_percent']}%")
                print(f"内存使用率: {system_metrics['memory_percent']}% (可用: {system_metrics['memory_available_gb']:.1f}GB)")
                print(f"磁盘使用率: {system_metrics['disk_percent']}% (可用: {system_metrics['disk_free_gb']:.1f}GB)")
                
                if 'error' not in redis_metrics:
                    print(f"Redis连接数: {redis_metrics['connected_clients']}")
                    print(f"Redis内存: {redis_metrics['used_memory_human']}")
                    print(f"Redis总命令数: {redis_metrics['total_commands_processed']}")
                else:
                    print(f"Redis监控错误: {redis_metrics['error']}")
                
                if 'error' not in server_metrics:
                    print(f"服务器响应时间: {server_metrics['response_time_ms']:.2f}ms")
                    print(f"服务器状态码: {server_metrics['status_code']}")
                else:
                    print(f"服务器监控错误: {server_metrics['error']}")
                
                # 检查性能警告
                self.check_performance_warnings(system_metrics, redis_metrics, server_metrics)
                
            except Exception as e:
                print(f"监控过程出错: {str(e)}")
            
            time.sleep(interval)
    
    def check_performance_warnings(self, system_metrics, redis_metrics, server_metrics):
        """检查性能警告"""
        warnings = []
        
        # 系统资源警告
        if system_metrics['cpu_percent'] > 80:
            warnings.append(f"CPU使用率过高: {system_metrics['cpu_percent']}%")
        
        if system_metrics['memory_percent'] > 85:
            warnings.append(f"内存使用率过高: {system_metrics['memory_percent']}%")
        
        if system_metrics['disk_percent'] > 90:
            warnings.append(f"磁盘使用率过高: {system_metrics['disk_percent']}%")
        
        # Redis警告
        if 'error' not in redis_metrics:
            if redis_metrics['connected_clients'] > 100:
                warnings.append(f"Redis连接数过多: {redis_metrics['connected_clients']}")
        
        # 服务器响应时间警告
        if 'error' not in server_metrics:
            if server_metrics['response_time_ms'] > 1000:
                warnings.append(f"服务器响应时间过长: {server_metrics['response_time_ms']:.2f}ms")
        
        if warnings:
            print("\n⚠️  性能警告:")
            for warning in warnings:
                print(f"  - {warning}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False

def main():
    monitor = PerformanceMonitor()
    
    print("开始性能监控...")
    print("按 Ctrl+C 停止监控")
    
    try:
        monitor.monitor_continuously(interval=10)
    except KeyboardInterrupt:
        print("\n停止性能监控")
        monitor.stop_monitoring()

if __name__ == '__main__':
    main() 