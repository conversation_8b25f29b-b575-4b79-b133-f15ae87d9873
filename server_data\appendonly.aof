*2
$6
SELECT
$1
0
*3
$3
SET
$15
exam_id_counter
$1
9
*3
$3
SET
$20
violation_id_counter
$2
13
*10
$5
RPUSH
$17
exam:9:violations
$469
{"id": 6, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: C:\\0teaching\\\u7b97\u6cd5\u8bbe\u8ba1\u4e0e\u5206\u6790\\\u4f8b\u5b50\u6e90\u7a0b\u5e8f\\2_\u4e8c\u5206\u67e5\u627e.exe\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 13:41:30", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_134130.png", "ip": "127.0.0.1"}
$610
{"id": 7, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\0teaching\\\u7b97\u6cd5\u8bbe\u8ba1\u4e0e\u5206\u6790\\\u4f8b\u5b50\u6e90\u7a0b\u5e8f\\2_\u4e8c\u5206\u67e5\u627e.cpp \u7684\u521b\u5efa\u65e5\u671f(2016-12-12 12:11:11)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-05-02 10:44:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-05-02 13:43:33", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_134333.png", "ip": "127.0.0.1"}
$522
{"id": 8, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\Users\\<USER>\\Desktop\\a.cpp \u7684\u521b\u5efa\u65e5\u671f(2025-04-29 19:06:16)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-05-02 10:44:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-05-02 13:43:46", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_134346.png", "ip": "127.0.0.1"}
$530
{"id": 9, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\Users\\<USER>\\Desktop\\Untitled1.cpp \u7684\u521b\u5efa\u65e5\u671f(2025-03-02 16:44:48)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-05-02 10:44:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-05-02 13:44:33", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_134433.png", "ip": "127.0.0.1"}
$402
{"id": 10, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u9009\u62e9C:\\Users\\<USER>\\Desktop\\Untitled6.exe\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 13:45:22", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_134522.png", "ip": "127.0.0.1"}
$376
{"id": 11, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: zq@GOJ: ~/project/supervise\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 14:12:48", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_141248.png", "ip": "127.0.0.1"}
$390
{"id": 12, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: C:\\Users\\<USER>\\Desktop\\Untitled6.exe\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 14:13:31", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_141331.png", "ip": "127.0.0.1"}
$376
{"id": 13, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: zq@GOJ: ~/project/supervise\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 14:13:58", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_141358.png", "ip": "127.0.0.1"}
*20
$5
HMSET
$16
exam:9:student:3
$2
id
$1
3
$8
username
$9
梁国祥
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
$2
ip
$9
127.0.0.1
$10
login_time
$19
2025-05-02 19:47:01
$11
last_active
$19
2025-05-02 19:47:02
$11
logout_time
$19
2025-05-02 19:47:13
*12
$5
HMSET
$17
exam:9:student:11
$2
id
$2
11
$8
username
$8
程响	2
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*6
$5
HMSET
$12
exam_configs
$1
9
$162
{"id": 9, "name": "sf", "start_time": "2025-05-02T10:44", "end_time": "2025-05-03T10:44", "created_at": "2025-05-02 10:45:00", "status": "active", "delay_min": 0}
$1
8
$164
{"id": 8, "name": "a", "start_time": "2025-04-29T19:55", "end_time": "2025-04-30T19:55", "created_at": "2025-04-29 19:55:29", "status": "completed", "delay_min": 0}
*12
$5
HMSET
$16
exam:9:student:5
$2
id
$1
5
$8
username
$9
王祉乔
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*4
$5
RPUSH
$23
exam:8:student:2:logins
$72
{"type": "login", "timestamp": "2025-04-29 20:17:09", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-04-29 20:17:20", "ip": "127.0.0.1"}
*17
$5
RPUSH
$28
exam:8:student:1:screenshots
$38
screenshot_1_8_2025-04-29_19-56-12.png
$38
screenshot_1_8_2025-04-29_19-56-42.png
$38
screenshot_1_8_2025-04-29_19-57-13.png
$38
screenshot_1_8_2025-04-29_19-57-43.png
$38
screenshot_1_8_2025-04-29_19-58-13.png
$38
screenshot_1_8_2025-04-29_19-58-44.png
$38
screenshot_1_8_2025-04-29_19-59-15.png
$38
screenshot_1_8_2025-04-29_19-59-45.png
$38
screenshot_1_8_2025-04-29_20-04-05.png
$38
screenshot_1_8_2025-04-29_20-04-36.png
$38
screenshot_1_8_2025-04-29_20-05-21.png
$38
screenshot_1_8_2025-04-29_20-05-52.png
$38
screenshot_1_8_2025-04-29_20-08-11.png
$38
screenshot_1_8_2025-04-29_20-08-42.png
$38
screenshot_1_8_2025-04-29_20-12-28.png
*7
$5
RPUSH
$17
exam:8:violations
$490
{"id": 1, "student_id": "1", "exam_id": "8", "username": "aa", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\Users\\<USER>\\Desktop\\a.cpp \u7684\u521b\u5efa\u65e5\u671f(2025-04-29 19:06:16)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-04-29 19:55:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-04-29 19:56:33", "screenshot_url": "/screenshots/aa-20250429_195633.png", "ip": "127.0.0.1"}
$490
{"id": 2, "student_id": "1", "exam_id": "8", "username": "aa", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\Users\\<USER>\\Desktop\\a.cpp \u7684\u521b\u5efa\u65e5\u671f(2025-04-29 19:06:16)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-04-29 19:55:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-04-29 19:57:25", "screenshot_url": "/screenshots/aa-20250429_195725.png", "ip": "127.0.0.1"}
$490
{"id": 3, "student_id": "1", "exam_id": "8", "username": "aa", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\Users\\<USER>\\Desktop\\a.cpp \u7684\u521b\u5efa\u65e5\u671f(2025-04-29 19:06:16)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-04-29 19:55:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-04-29 19:58:30", "screenshot_url": "/screenshots/aa-20250429_195830.png", "ip": "127.0.0.1"}
$490
{"id": 4, "student_id": "1", "exam_id": "8", "username": "aa", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\Users\\<USER>\\Desktop\\a.cpp \u7684\u521b\u5efa\u65e5\u671f(2025-04-29 19:06:16)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-04-29 19:55:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-04-29 19:59:03", "screenshot_url": "/screenshots/aa-20250429_195903.png", "ip": "127.0.0.1"}
$490
{"id": 5, "student_id": "1", "exam_id": "8", "username": "aa", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\Users\\<USER>\\Desktop\\a.cpp \u7684\u521b\u5efa\u65e5\u671f(2025-04-29 19:06:16)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-04-29 19:55:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-04-29 20:08:26", "screenshot_url": "/screenshots/aa-20250429_200826.png", "ip": "127.0.0.1"}
*12
$5
HMSET
$16
exam:8:student:3
$2
id
$1
3
$8
username
$2
cc
$7
exam_id
$1
8
$6
status
$7
offline
$10
created_at
$19
2025-04-29 19:55:37
*12
$5
HMSET
$16
exam:9:student:6
$2
id
$1
6
$8
username
$9
孟晨健
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*12
$5
HMSET
$17
exam:9:student:16
$2
id
$2
16
$8
username
$9
刘文睿
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*12
$5
HMSET
$17
exam:9:student:12
$2
id
$2
12
$8
username
$9
柳思彤
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*12
$5
HMSET
$17
exam:9:student:18
$2
id
$2
18
$8
username
$9
苏建烨
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*12
$5
RPUSH
$23
exam:8:student:1:logins
$72
{"type": "login", "timestamp": "2025-04-29 19:56:03", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-04-29 20:00:13", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-04-29 20:03:57", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-04-29 20:05:02", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-04-29 20:05:12", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-04-29 20:06:13", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-04-29 20:08:02", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-04-29 20:09:08", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-04-29 20:12:19", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-04-29 20:12:47", "ip": "127.0.0.1"}
*12
$5
HMSET
$17
exam:9:student:13
$2
id
$2
13
$8
username
$9
钟振宇
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*36
$5
RPUSH
$28
exam:9:student:3:screenshots
$38
screenshot_3_9_2025-05-02_13-31-06.png
$38
screenshot_3_9_2025-05-02_13-41-08.png
$38
screenshot_3_9_2025-05-02_13-41-38.png
$38
screenshot_3_9_2025-05-02_13-43-27.png
$38
screenshot_3_9_2025-05-02_13-43-57.png
$38
screenshot_3_9_2025-05-02_13-44-28.png
$38
screenshot_3_9_2025-05-02_13-44-58.png
$38
screenshot_3_9_2025-05-02_13-45-29.png
$38
screenshot_3_9_2025-05-02_14-00-51.png
$38
screenshot_3_9_2025-05-02_14-01-22.png
$38
screenshot_3_9_2025-05-02_14-01-52.png
$38
screenshot_3_9_2025-05-02_14-02-22.png
$38
screenshot_3_9_2025-05-02_14-04-05.png
$38
screenshot_3_9_2025-05-02_14-04-36.png
$38
screenshot_3_9_2025-05-02_14-05-06.png
$38
screenshot_3_9_2025-05-02_14-05-36.png
$38
screenshot_3_9_2025-05-02_14-09-56.png
$38
screenshot_3_9_2025-05-02_14-10-27.png
$38
screenshot_3_9_2025-05-02_14-10-57.png
$38
screenshot_3_9_2025-05-02_14-11-27.png
$38
screenshot_3_9_2025-05-02_14-11-58.png
$38
screenshot_3_9_2025-05-02_14-12-35.png
$38
screenshot_3_9_2025-05-02_14-13-06.png
$38
screenshot_3_9_2025-05-02_14-13-36.png
$38
screenshot_3_9_2025-05-02_17-32-33.png
$38
screenshot_3_9_2025-05-02_17-34-08.png
$38
screenshot_3_9_2025-05-02_17-36-06.png
$38
screenshot_3_9_2025-05-02_19-41-29.png
$38
screenshot_3_9_2025-05-02_19-42-09.png
$38
screenshot_3_9_2025-05-02_19-42-40.png
$38
screenshot_3_9_2025-05-02_19-43-10.png
$38
screenshot_3_9_2025-05-02_19-45-16.png
$38
screenshot_3_9_2025-05-02_19-45-46.png
$38
screenshot_3_9_2025-05-02_19-47-09.png
*20
$5
HMSET
$16
exam:8:student:1
$2
id
$1
1
$8
username
$2
aa
$7
exam_id
$1
8
$6
status
$7
offline
$10
created_at
$19
2025-04-29 19:55:37
$2
ip
$9
127.0.0.1
$10
login_time
$19
2025-04-29 20:12:19
$11
last_active
$19
2025-04-29 20:12:20
$11
logout_time
$19
2025-04-29 20:12:47
*12
$5
HMSET
$17
exam:9:student:17
$2
id
$2
17
$8
username
$9
黄健琛
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*3
$5
RPUSH
$28
exam:8:student:2:screenshots
$38
screenshot_2_8_2025-04-29_20-17-15.png
*12
$5
HMSET
$17
exam:9:student:14
$2
id
$2
14
$8
username
$9
张雯亭
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*12
$5
HMSET
$16
exam:9:student:1
$2
id
$1
1
$8
username
$9
麦思睿
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*3
$3
SET
$25
exam:8:student_id_counter
$1
3
*12
$5
HMSET
$17
exam:9:student:20
$2
id
$2
20
$8
username
$9
陈紫名
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*12
$5
HMSET
$17
exam:9:student:19
$2
id
$2
19
$8
username
$9
袁广浩
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*15
$5
RPUSH
$10
violations
$490
{"id": 1, "student_id": "1", "exam_id": "8", "username": "aa", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\Users\\<USER>\\Desktop\\a.cpp \u7684\u521b\u5efa\u65e5\u671f(2025-04-29 19:06:16)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-04-29 19:55:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-04-29 19:56:33", "screenshot_url": "/screenshots/aa-20250429_195633.png", "ip": "127.0.0.1"}
$490
{"id": 2, "student_id": "1", "exam_id": "8", "username": "aa", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\Users\\<USER>\\Desktop\\a.cpp \u7684\u521b\u5efa\u65e5\u671f(2025-04-29 19:06:16)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-04-29 19:55:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-04-29 19:57:25", "screenshot_url": "/screenshots/aa-20250429_195725.png", "ip": "127.0.0.1"}
$490
{"id": 3, "student_id": "1", "exam_id": "8", "username": "aa", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\Users\\<USER>\\Desktop\\a.cpp \u7684\u521b\u5efa\u65e5\u671f(2025-04-29 19:06:16)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-04-29 19:55:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-04-29 19:58:30", "screenshot_url": "/screenshots/aa-20250429_195830.png", "ip": "127.0.0.1"}
$490
{"id": 4, "student_id": "1", "exam_id": "8", "username": "aa", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\Users\\<USER>\\Desktop\\a.cpp \u7684\u521b\u5efa\u65e5\u671f(2025-04-29 19:06:16)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-04-29 19:55:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-04-29 19:59:03", "screenshot_url": "/screenshots/aa-20250429_195903.png", "ip": "127.0.0.1"}
$490
{"id": 5, "student_id": "1", "exam_id": "8", "username": "aa", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\Users\\<USER>\\Desktop\\a.cpp \u7684\u521b\u5efa\u65e5\u671f(2025-04-29 19:06:16)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-04-29 19:55:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-04-29 20:08:26", "screenshot_url": "/screenshots/aa-20250429_200826.png", "ip": "127.0.0.1"}
$469
{"id": 6, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: C:\\0teaching\\\u7b97\u6cd5\u8bbe\u8ba1\u4e0e\u5206\u6790\\\u4f8b\u5b50\u6e90\u7a0b\u5e8f\\2_\u4e8c\u5206\u67e5\u627e.exe\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 13:41:30", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_134130.png", "ip": "127.0.0.1"}
$610
{"id": 7, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\0teaching\\\u7b97\u6cd5\u8bbe\u8ba1\u4e0e\u5206\u6790\\\u4f8b\u5b50\u6e90\u7a0b\u5e8f\\2_\u4e8c\u5206\u67e5\u627e.cpp \u7684\u521b\u5efa\u65e5\u671f(2016-12-12 12:11:11)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-05-02 10:44:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-05-02 13:43:33", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_134333.png", "ip": "127.0.0.1"}
$522
{"id": 8, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\Users\\<USER>\\Desktop\\a.cpp \u7684\u521b\u5efa\u65e5\u671f(2025-04-29 19:06:16)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-05-02 10:44:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-05-02 13:43:46", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_134346.png", "ip": "127.0.0.1"}
$530
{"id": 9, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "Dev-C++ \u5f53\u524d\u6253\u5f00\u6587\u4ef6: C:\\Users\\<USER>\\Desktop\\Untitled1.cpp \u7684\u521b\u5efa\u65e5\u671f(2025-03-02 16:44:48)\u65e9\u4e8e\u8003\u8bd5\u5f00\u59cb\u65f6\u95f4(2025-05-02 10:44:00)\uff0c\u8bf7\u6ce8\u610f\u662f\u5426\u4e3a\u8003\u8bd5\u524d\u51c6\u5907\u7684\u4ee3\u7801\u6587\u4ef6", "timestamp": "2025-05-02 13:44:33", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_134433.png", "ip": "127.0.0.1"}
$402
{"id": 10, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u9009\u62e9C:\\Users\\<USER>\\Desktop\\Untitled6.exe\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 13:45:22", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_134522.png", "ip": "127.0.0.1"}
$376
{"id": 11, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: zq@GOJ: ~/project/supervise\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 14:12:48", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_141248.png", "ip": "127.0.0.1"}
$390
{"id": 12, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: C:\\Users\\<USER>\\Desktop\\Untitled6.exe\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 14:13:31", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_141331.png", "ip": "127.0.0.1"}
$376
{"id": 13, "student_id": "3", "exam_id": "9", "username": "\u6881\u56fd\u7965", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: zq@GOJ: ~/project/supervise\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 14:13:58", "screenshot_url": "/screenshots/\u6881\u56fd\u7965-20250502_141358.png", "ip": "127.0.0.1"}
*12
$5
HMSET
$16
exam:9:student:9
$2
id
$1
9
$8
username
$9
周启航
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*12
$5
HMSET
$17
exam:9:student:10
$2
id
$2
10
$8
username
$9
陈泊成
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*20
$5
HMSET
$16
exam:8:student:2
$2
id
$1
2
$8
username
$2
bb
$7
exam_id
$1
8
$6
status
$7
offline
$10
created_at
$19
2025-04-29 19:55:37
$2
ip
$9
127.0.0.1
$10
login_time
$19
2025-04-29 20:17:09
$11
last_active
$19
2025-04-29 20:17:09
$11
logout_time
$19
2025-04-29 20:17:20
*12
$5
HMSET
$17
exam:9:student:15
$2
id
$2
15
$8
username
$9
梁志鹏
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*12
$5
HMSET
$16
exam:9:student:4
$2
id
$1
4
$8
username
$9
甄浩鹏
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*3
$3
SET
$25
exam:9:student_id_counter
$2
20
*12
$5
HMSET
$16
exam:9:student:2
$2
id
$1
2
$8
username
$9
冯俊江
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*12
$5
HMSET
$16
exam:9:student:8
$2
id
$1
8
$8
username
$9
赵祖恩
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*30
$5
RPUSH
$23
exam:9:student:3:logins
$72
{"type": "login", "timestamp": "2025-05-02 13:30:10", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-05-02 13:30:23", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-05-02 13:30:58", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-05-02 13:31:24", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-05-02 13:40:59", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-05-02 13:41:59", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-05-02 13:43:16", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-05-02 13:45:33", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-05-02 14:00:46", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-05-02 14:02:39", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-05-02 14:03:57", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-05-02 14:05:37", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-05-02 14:09:45", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-05-02 14:12:15", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-05-02 14:12:28", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-05-02 14:14:04", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-05-02 17:32:25", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-05-02 17:32:51", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-05-02 17:33:58", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-05-02 17:34:13", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-05-02 17:36:01", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-05-02 19:38:49", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-05-02 19:41:31", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-05-02 19:42:05", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-05-02 19:45:09", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-05-02 19:45:50", "ip": "127.0.0.1"}
$72
{"type": "login", "timestamp": "2025-05-02 19:47:01", "ip": "127.0.0.1"}
$73
{"type": "logout", "timestamp": "2025-05-02 19:47:13", "ip": "127.0.0.1"}
*12
$5
HMSET
$16
exam:9:student:7
$2
id
$1
7
$8
username
$9
陈文海
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 10:45:00
*2
$6
SELECT
$1
0
*1
$5
MULTI
*16
$5
HMSET
$16
exam:9:student:3
$2
id
$1
3
$8
username
$9
梁国祥
$2
ip
$9
127.0.0.1
$7
exam_id
$1
9
$10
login_time
$19
2025-05-02 19:53:28
$11
last_active
$19
2025-05-02 19:53:28
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$23
exam:9:student:3:logins
$72
{"type": "login", "timestamp": "2025-05-02 19:53:28", "ip": "127.0.0.1"}
*4
$4
HSET
$16
exam:9:student:3
$11
last_active
$19
2025-05-02 19:53:29
*3
$5
RPUSH
$28
exam:9:student:3:screenshots
$38
screenshot_3_9_2025-05-02_19-53-33.png
*4
$4
HSET
$16
exam:9:student:3
$11
last_active
$19
2025-05-02 19:53:59
*3
$5
RPUSH
$28
exam:9:student:3:screenshots
$38
screenshot_3_9_2025-05-02_19-54-04.png
*4
$4
HSET
$16
exam:9:student:3
$6
status
$7
offline
*4
$4
HSET
$16
exam:9:student:3
$11
logout_time
$19
2025-05-02 19:54:18
*3
$5
RPUSH
$23
exam:9:student:3:logins
$73
{"type": "logout", "timestamp": "2025-05-02 19:54:18", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$16
exam:9:student:3
$2
id
$1
3
$8
username
$9
梁国祥
$2
ip
$9
127.0.0.1
$7
exam_id
$1
9
$10
login_time
$19
2025-05-02 19:54:50
$11
last_active
$19
2025-05-02 19:54:50
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$23
exam:9:student:3:logins
$72
{"type": "login", "timestamp": "2025-05-02 19:54:50", "ip": "127.0.0.1"}
*4
$4
HSET
$16
exam:9:student:3
$11
last_active
$19
2025-05-02 19:54:50
*3
$5
RPUSH
$28
exam:9:student:3:screenshots
$38
screenshot_3_9_2025-05-02_19-54-59.png
*4
$4
HSET
$16
exam:9:student:3
$11
last_active
$19
2025-05-02 19:55:20
*3
$5
RPUSH
$28
exam:9:student:3:screenshots
$38
screenshot_3_9_2025-05-02_19-55-29.png
*4
$4
HSET
$16
exam:9:student:3
$6
status
$7
offline
*4
$4
HSET
$16
exam:9:student:3
$11
logout_time
$19
2025-05-02 19:55:33
*3
$5
RPUSH
$23
exam:9:student:3:logins
$73
{"type": "logout", "timestamp": "2025-05-02 19:55:33", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$16
exam:9:student:3
$2
id
$1
3
$8
username
$9
梁国祥
$2
ip
$9
127.0.0.1
$7
exam_id
$1
9
$10
login_time
$19
2025-05-02 20:13:25
$11
last_active
$19
2025-05-02 20:13:25
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$23
exam:9:student:3:logins
$72
{"type": "login", "timestamp": "2025-05-02 20:13:25", "ip": "127.0.0.1"}
*4
$4
HSET
$16
exam:9:student:3
$11
last_active
$19
2025-05-02 20:13:26
*3
$5
RPUSH
$28
exam:9:student:3:screenshots
$38
screenshot_3_9_2025-05-02_20-13-43.png
*4
$4
HSET
$16
exam:9:student:3
$11
last_active
$19
2025-05-02 20:13:56
*3
$5
RPUSH
$28
exam:9:student:3:screenshots
$38
screenshot_3_9_2025-05-02_20-14-14.png
*4
$4
HSET
$16
exam:9:student:3
$11
last_active
$19
2025-05-02 20:14:26
*3
$5
RPUSH
$28
exam:9:student:3:screenshots
$38
screenshot_3_9_2025-05-02_20-14-44.png
*4
$4
HSET
$16
exam:9:student:3
$11
last_active
$19
2025-05-02 20:14:56
*4
$4
HSET
$16
exam:9:student:3
$6
status
$7
offline
*4
$4
HSET
$16
exam:9:student:3
$11
logout_time
$19
2025-05-02 20:15:12
*3
$5
RPUSH
$28
exam:9:student:3:screenshots
$38
screenshot_3_9_2025-05-02_20-15-15.png
*4
$4
HSET
$16
exam:9:student:3
$11
last_active
$19
2025-05-02 20:15:26
*4
$4
HSET
$16
exam:9:student:3
$6
status
$6
online
*3
$5
RPUSH
$28
exam:9:student:3:screenshots
$38
screenshot_3_9_2025-05-02_20-15-45.png
*4
$4
HSET
$16
exam:9:student:3
$6
status
$7
offline
*4
$4
HSET
$16
exam:9:student:3
$11
logout_time
$19
2025-05-02 20:15:46
*4
$4
HSET
$16
exam:9:student:3
$11
last_active
$19
2025-05-02 20:15:56
*4
$4
HSET
$16
exam:9:student:3
$6
status
$6
online
*3
$5
RPUSH
$28
exam:9:student:3:screenshots
$38
screenshot_3_9_2025-05-02_20-16-15.png
*4
$4
HSET
$16
exam:9:student:3
$11
last_active
$19
2025-05-02 20:16:26
*4
$4
HSET
$16
exam:9:student:3
$6
status
$7
offline
*4
$4
HSET
$16
exam:9:student:3
$11
logout_time
$19
2025-05-02 20:16:26
*3
$5
RPUSH
$28
exam:9:student:3:screenshots
$38
screenshot_3_9_2025-05-02_20-16-46.png
*4
$4
HSET
$16
exam:9:student:3
$11
last_active
$19
2025-05-02 20:16:56
*4
$4
HSET
$16
exam:9:student:3
$6
status
$6
online
*4
$4
HSET
$16
exam:9:student:3
$6
status
$7
offline
*4
$4
HSET
$16
exam:9:student:3
$11
logout_time
$19
2025-05-02 20:17:46
*3
$6
INCRBY
$25
exam:9:student_id_counter
$1
1
*12
$5
HMSET
$17
exam:9:student:21
$2
id
$2
21
$8
username
$1
a
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 20:32:50
*1
$5
MULTI
*16
$5
HMSET
$17
exam:9:student:21
$2
id
$2
21
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$1
9
$10
login_time
$19
2025-05-02 20:33:04
$11
last_active
$19
2025-05-02 20:33:04
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:9:student:21:logins
$72
{"type": "login", "timestamp": "2025-05-02 20:33:04", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:33:04
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-33-11.png
*4
$4
HSET
$17
exam:9:student:21
$6
status
$7
offline
*4
$4
HSET
$17
exam:9:student:21
$11
logout_time
$19
2025-05-02 20:33:20
*3
$5
RPUSH
$24
exam:9:student:21:logins
$73
{"type": "logout", "timestamp": "2025-05-02 20:33:20", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:9:student:21
$2
id
$2
21
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$1
9
$10
login_time
$19
2025-05-02 20:33:26
$11
last_active
$19
2025-05-02 20:33:26
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:9:student:21:logins
$72
{"type": "login", "timestamp": "2025-05-02 20:33:26", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:33:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-33-35.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:33:57
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 14, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:33:57", "screenshot_url": "/screenshots/a-20250502_203357.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 14, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:33:57", "screenshot_url": "/screenshots/a-20250502_203357.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-34-05.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:34:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-34-36.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:34:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-35-06.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:35:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-35-36.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:35:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-36-06.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:36:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-36-37.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:36:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-37-07.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:37:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-37-37.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:37:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-38-08.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:38:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-38-38.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:38:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-39-08.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:39:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-39-39.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:39:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-40-09.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:40:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-40-39.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:40:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-41-10.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:41:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-41-40.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:41:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-42-10.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:42:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-42-40.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:42:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-43-11.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:43:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-43-41.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:43:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-44-11.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:44:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-44-41.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:44:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-45-12.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:45:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-45-42.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:45:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-46-12.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:46:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-46-43.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:46:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-47-13.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:47:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-47-43.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:47:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-48-13.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:48:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-48-44.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:48:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-49-14.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:49:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-49-44.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:49:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-50-14.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:50:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-50-45.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:50:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-51-15.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:51:27
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-51-45.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:51:57
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-52-16.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:52:27
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 15, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:52:32", "screenshot_url": "/screenshots/a-20250502_205232.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 15, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:52:32", "screenshot_url": "/screenshots/a-20250502_205232.png", "ip": "127.0.0.1"}
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 16, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:52:44", "screenshot_url": "/screenshots/a-20250502_205244.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 16, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:52:44", "screenshot_url": "/screenshots/a-20250502_205244.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-52-46.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 17, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:52:56", "screenshot_url": "/screenshots/a-20250502_205256.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 17, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:52:56", "screenshot_url": "/screenshots/a-20250502_205256.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:52:57
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 18, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:53:08", "screenshot_url": "/screenshots/a-20250502_205308.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 18, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:53:08", "screenshot_url": "/screenshots/a-20250502_205308.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-53-17.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:53:27
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 19, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:53:30", "screenshot_url": "/screenshots/a-20250502_205330.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 19, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:53:30", "screenshot_url": "/screenshots/a-20250502_205330.png", "ip": "127.0.0.1"}
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 20, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:53:42", "screenshot_url": "/screenshots/a-20250502_205342.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 20, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:53:42", "screenshot_url": "/screenshots/a-20250502_205342.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-53-47.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 21, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:53:54", "screenshot_url": "/screenshots/a-20250502_205354.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 21, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:53:54", "screenshot_url": "/screenshots/a-20250502_205354.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:53:57
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 22, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:54:06", "screenshot_url": "/screenshots/a-20250502_205406.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 22, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:54:06", "screenshot_url": "/screenshots/a-20250502_205406.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-54-17.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 23, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:54:18", "screenshot_url": "/screenshots/a-20250502_205418.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 23, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:54:18", "screenshot_url": "/screenshots/a-20250502_205418.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:54:28
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 24, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:54:31", "screenshot_url": "/screenshots/a-20250502_205430.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 24, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:54:31", "screenshot_url": "/screenshots/a-20250502_205430.png", "ip": "127.0.0.1"}
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 25, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:54:43", "screenshot_url": "/screenshots/a-20250502_205443.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 25, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:54:43", "screenshot_url": "/screenshots/a-20250502_205443.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-54-48.png
*4
$4
HSET
$17
exam:9:student:21
$6
status
$7
offline
*4
$4
HSET
$17
exam:9:student:21
$11
logout_time
$19
2025-05-02 20:54:50
*3
$5
RPUSH
$24
exam:9:student:21:logins
$73
{"type": "logout", "timestamp": "2025-05-02 20:54:50", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:9:student:21
$2
id
$2
21
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$1
9
$10
login_time
$19
2025-05-02 20:56:34
$11
last_active
$19
2025-05-02 20:56:34
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:9:student:21:logins
$72
{"type": "login", "timestamp": "2025-05-02 20:56:34", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:56:35
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-56-41.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:57:05
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-57-11.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:57:35
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_20-57-42.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$382
{"id": 26, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: T11\u8bc4\u5ba1\u5de5\u4f5c\u6587\u6863.pdf - Adobe Acrobat Pro DC\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:57:47", "screenshot_url": "/screenshots/a-20250502_205747.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$382
{"id": 26, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: T11\u8bc4\u5ba1\u5de5\u4f5c\u6587\u6863.pdf - Adobe Acrobat Pro DC\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 20:57:47", "screenshot_url": "/screenshots/a-20250502_205747.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 20:58:05
*4
$4
HSET
$17
exam:9:student:21
$6
status
$7
offline
*4
$4
HSET
$17
exam:9:student:21
$11
logout_time
$19
2025-05-02 20:58:07
*3
$5
RPUSH
$24
exam:9:student:21:logins
$73
{"type": "logout", "timestamp": "2025-05-02 20:58:07", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:9:student:21
$2
id
$2
21
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$1
9
$10
login_time
$19
2025-05-02 21:03:05
$11
last_active
$19
2025-05-02 21:03:05
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:9:student:21:logins
$72
{"type": "login", "timestamp": "2025-05-02 21:03:05", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:03:06
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_21-03-12.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 27, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:03:13", "screenshot_url": "/screenshots/a-20250502_210313.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 27, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:03:13", "screenshot_url": "/screenshots/a-20250502_210313.png", "ip": "127.0.0.1"}
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 28, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:03:25", "screenshot_url": "/screenshots/a-20250502_210325.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 28, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:03:25", "screenshot_url": "/screenshots/a-20250502_210325.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:03:36
*4
$4
HSET
$17
exam:9:student:21
$6
status
$7
offline
*4
$4
HSET
$17
exam:9:student:21
$11
logout_time
$19
2025-05-02 21:03:39
*3
$5
RPUSH
$24
exam:9:student:21:logins
$73
{"type": "logout", "timestamp": "2025-05-02 21:03:39", "ip": "127.0.0.1"}
*3
$6
INCRBY
$25
exam:9:student_id_counter
$1
1
*12
$5
HMSET
$17
exam:9:student:22
$2
id
$2
22
$8
username
$1
b
$7
exam_id
$1
9
$6
status
$7
offline
$10
created_at
$19
2025-05-02 21:04:13
*1
$5
MULTI
*16
$5
HMSET
$17
exam:9:student:22
$2
id
$2
22
$8
username
$1
b
$2
ip
$9
127.0.0.1
$7
exam_id
$1
9
$10
login_time
$19
2025-05-02 21:04:39
$11
last_active
$19
2025-05-02 21:04:39
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:9:student:22:logins
$72
{"type": "login", "timestamp": "2025-05-02 21:04:39", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:22
$11
last_active
$19
2025-05-02 21:04:39
*3
$5
RPUSH
$29
exam:9:student:22:screenshots
$39
screenshot_22_9_2025-05-02_21-04-47.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 29, "student_id": "22", "exam_id": "9", "username": "b", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:04:50", "screenshot_url": "/screenshots/b-20250502_210450.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 29, "student_id": "22", "exam_id": "9", "username": "b", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:04:50", "screenshot_url": "/screenshots/b-20250502_210450.png", "ip": "127.0.0.1"}
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 30, "student_id": "22", "exam_id": "9", "username": "b", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:05:02", "screenshot_url": "/screenshots/b-20250502_210502.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 30, "student_id": "22", "exam_id": "9", "username": "b", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:05:02", "screenshot_url": "/screenshots/b-20250502_210502.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:22
$11
last_active
$19
2025-05-02 21:05:09
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 31, "student_id": "22", "exam_id": "9", "username": "b", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:05:14", "screenshot_url": "/screenshots/b-20250502_210514.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 31, "student_id": "22", "exam_id": "9", "username": "b", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:05:14", "screenshot_url": "/screenshots/b-20250502_210514.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:9:student:22:screenshots
$39
screenshot_22_9_2025-05-02_21-05-17.png
*4
$4
HSET
$17
exam:9:student:22
$6
status
$7
offline
*4
$4
HSET
$17
exam:9:student:22
$11
logout_time
$19
2025-05-02 21:05:20
*3
$5
RPUSH
$24
exam:9:student:22:logins
$73
{"type": "logout", "timestamp": "2025-05-02 21:05:20", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:9:student:22
$2
id
$2
22
$8
username
$1
b
$2
ip
$9
127.0.0.1
$7
exam_id
$1
9
$10
login_time
$19
2025-05-02 21:10:48
$11
last_active
$19
2025-05-02 21:10:48
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:9:student:22:logins
$72
{"type": "login", "timestamp": "2025-05-02 21:10:48", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:22
$11
last_active
$19
2025-05-02 21:10:48
*3
$5
RPUSH
$29
exam:9:student:22:screenshots
$39
screenshot_22_9_2025-05-02_21-10-55.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 32, "student_id": "22", "exam_id": "9", "username": "b", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:10:57", "screenshot_url": "/screenshots/b-20250502_211057.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 32, "student_id": "22", "exam_id": "9", "username": "b", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:10:57", "screenshot_url": "/screenshots/b-20250502_211057.png", "ip": "127.0.0.1"}
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 33, "student_id": "22", "exam_id": "9", "username": "b", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:11:09", "screenshot_url": "/screenshots/b-20250502_211109.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 33, "student_id": "22", "exam_id": "9", "username": "b", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:11:09", "screenshot_url": "/screenshots/b-20250502_211109.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:22
$6
status
$7
offline
*4
$4
HSET
$17
exam:9:student:22
$11
logout_time
$19
2025-05-02 21:11:14
*3
$5
RPUSH
$24
exam:9:student:22:logins
$73
{"type": "logout", "timestamp": "2025-05-02 21:11:14", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:9:student:21
$2
id
$2
21
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$1
9
$10
login_time
$19
2025-05-02 21:12:59
$11
last_active
$19
2025-05-02 21:12:59
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:9:student:21:logins
$72
{"type": "login", "timestamp": "2025-05-02 21:12:59", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:12:59
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_21-13-04.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 34, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:13:06", "screenshot_url": "/screenshots/a-20250502_211306.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 34, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:13:06", "screenshot_url": "/screenshots/a-20250502_211306.png", "ip": "127.0.0.1"}
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 35, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:13:20", "screenshot_url": "/screenshots/a-20250502_211320.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 35, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:13:20", "screenshot_url": "/screenshots/a-20250502_211320.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:13:29
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 36, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:13:34", "screenshot_url": "/screenshots/a-20250502_211334.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 36, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:13:34", "screenshot_url": "/screenshots/a-20250502_211334.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_21-13-38.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:13:59
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_21-14-09.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:14:29
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_21-14-40.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:14:59
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_21-15-10.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:15:29
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_21-15-41.png
*4
$4
HSET
$17
exam:9:student:21
$6
status
$7
offline
*4
$4
HSET
$17
exam:9:student:21
$11
logout_time
$19
2025-05-02 21:15:50
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:15:59
*4
$4
HSET
$17
exam:9:student:21
$6
status
$6
online
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_21-16-11.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:16:29
*4
$4
HSET
$17
exam:9:student:21
$6
status
$7
offline
*4
$4
HSET
$17
exam:9:student:21
$11
logout_time
$19
2025-05-02 21:16:36
*3
$5
RPUSH
$24
exam:9:student:21:logins
$73
{"type": "logout", "timestamp": "2025-05-02 21:16:36", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:9:student:21
$2
id
$2
21
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$1
9
$10
login_time
$19
2025-05-02 21:24:47
$11
last_active
$19
2025-05-02 21:24:47
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:9:student:21:logins
$72
{"type": "login", "timestamp": "2025-05-02 21:24:47", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:24:47
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_21-24-55.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 37, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:24:57", "screenshot_url": "/screenshots/a-20250502_212457.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 37, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:24:57", "screenshot_url": "/screenshots/a-20250502_212457.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:25:17
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_21-25-26.png
*1
$5
MULTI
*16
$5
HMSET
$17
exam:9:student:22
$2
id
$2
22
$8
username
$1
b
$2
ip
$9
127.0.0.1
$7
exam_id
$1
9
$10
login_time
$19
2025-05-02 21:25:33
$11
last_active
$19
2025-05-02 21:25:33
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:9:student:22:logins
$72
{"type": "login", "timestamp": "2025-05-02 21:25:33", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:22
$11
last_active
$19
2025-05-02 21:25:33
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 38, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:25:40", "screenshot_url": "/screenshots/a-20250502_212540.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$17
exam:9:violations
$326
{"id": 38, "student_id": "21", "exam_id": "9", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-02 21:25:40", "screenshot_url": "/screenshots/a-20250502_212540.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:9:student:22:screenshots
$39
screenshot_22_9_2025-05-02_21-25-42.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:25:47
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_21-25-58.png
*4
$4
HSET
$17
exam:9:student:22
$11
last_active
$19
2025-05-02 21:26:03
*3
$5
RPUSH
$29
exam:9:student:22:screenshots
$39
screenshot_22_9_2025-05-02_21-26-13.png
*4
$4
HSET
$17
exam:9:student:22
$6
status
$7
offline
*4
$4
HSET
$17
exam:9:student:22
$11
logout_time
$19
2025-05-02 21:26:16
*3
$5
RPUSH
$24
exam:9:student:22:logins
$73
{"type": "logout", "timestamp": "2025-05-02 21:26:16", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:26:17
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_21-26-28.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:26:47
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_21-26-58.png
*4
$4
HSET
$17
exam:9:student:21
$11
last_active
$19
2025-05-02 21:27:17
*3
$5
RPUSH
$29
exam:9:student:21:screenshots
$39
screenshot_21_9_2025-05-02_21-27-29.png
*4
$4
HSET
$17
exam:9:student:21
$6
status
$7
offline
*4
$4
HSET
$17
exam:9:student:21
$11
logout_time
$19
2025-05-02 21:27:34
*3
$5
RPUSH
$24
exam:9:student:21:logins
$73
{"type": "logout", "timestamp": "2025-05-02 21:27:34", "ip": "127.0.0.1"}
*4
$4
HSET
$12
exam_configs
$1
9
$165
{"id": 9, "name": "sf", "start_time": "2025-05-02T10:44", "end_time": "2025-05-03T10:44", "created_at": "2025-05-02 10:45:00", "status": "completed", "delay_min": 0}
*3
$6
INCRBY
$15
exam_id_counter
$1
1
*4
$4
HSET
$12
exam_configs
$2
10
$215
{"id": 10, "name": "a", "start_time": "2025-05-03T19:58", "end_time": "2025-06-08T19:58", "created_at": "2025-05-03 19:59:21", "status": "pending", "default_url": "https://vjudge.net/contest/714120", "delay_min": 0}
*3
$6
INCRBY
$26
exam:10:student_id_counter
$1
1
*12
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$7
exam_id
$2
10
$6
status
$7
offline
$10
created_at
$19
2025-05-03 19:59:21
*4
$4
HSET
$12
exam_configs
$2
10
$214
{"id": 10, "name": "a", "start_time": "2025-05-03T19:58", "end_time": "2025-06-08T19:58", "created_at": "2025-05-03 19:59:21", "status": "active", "default_url": "https://vjudge.net/contest/714120", "delay_min": 0}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-03 19:59:48
$11
last_active
$19
2025-05-03 19:59:48
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-03 19:59:48", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 19:59:49
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-00-09.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:00:20
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-00-41.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:00:50
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-01-12.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:01:20
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-03 20:01:39
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-03 20:01:39", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-03 20:02:34
$11
last_active
$19
2025-05-03 20:02:34
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-03 20:02:34", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:02:34
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-02-42.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:03:04
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-03-13.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:03:34
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-03-44.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:04:04
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-04-14.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:04:34
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-04-45.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:05:04
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-05-15.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:05:34
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-05-46.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:06:04
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-06-17.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:06:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-06-47.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:07:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-07-18.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:07:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-07-48.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:08:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-08-19.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:08:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-08-50.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:09:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-09-20.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:09:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-09-51.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:10:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-10-21.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:10:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-10-52.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:11:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-11-23.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:11:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-11-53.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:12:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-12-24.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:12:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-12-55.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:13:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-13-25.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:13:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-13-56.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:14:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-14-26.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:14:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-14-57.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:15:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-15-27.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:15:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-15-58.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:16:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-16-29.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:16:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-16-59.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:17:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-17-30.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:17:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-18-00.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:18:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-18-31.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:18:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-19-02.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:19:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-19-32.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:19:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-20-03.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:20:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-20-33.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:20:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-21-04.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:21:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-21-35.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:21:35
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:22:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-22-05.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:22:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-22-36.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:23:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-23-07.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:23:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-23-37.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:24:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-24-08.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:24:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-24-38.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:25:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-25-09.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:25:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-25-40.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:26:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-26-10.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:26:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-26-41.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:27:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-27-11.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:27:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-27-42.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:28:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-28-12.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:28:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-28-43.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:29:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-29-14.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:29:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-29-44.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:30:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-30-15.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:30:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-30-45.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:31:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-31-16.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:31:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-31-47.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:32:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-32-17.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:32:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-32-48.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:33:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-33-18.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:33:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-33-49.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:34:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-34-20.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:34:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-34-50.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:35:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-35-21.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:35:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-35-51.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:36:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-36-22.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:36:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-36-53.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:37:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-37-23.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:37:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-37-54.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:38:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-38-24.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:38:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-38-55.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:39:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-39-26.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:39:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-39-56.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:40:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-40-27.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:40:35
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-40-57.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:41:05
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-41-28.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:41:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-41-59.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:42:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-42-29.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:42:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-43-00.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:43:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-43-31.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:43:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-44-01.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:44:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-44-32.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:44:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-45-03.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:45:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-45-33.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:45:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-46-04.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:46:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-46-34.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:46:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-47-05.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:47:06
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:47:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-47-36.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:48:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-48-06.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:48:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-48-37.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:49:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-49-08.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:49:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-49-38.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:50:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-50-09.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:50:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-50-39.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:51:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-51-10.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:51:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-51-41.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:52:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-52-11.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:52:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-52-42.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:53:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-53-12.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:53:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-53-43.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:54:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-54-14.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:54:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-54-44.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:55:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-55-15.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:55:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-55-45.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:56:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-56-16.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:56:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-56-47.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:57:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-57-17.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:57:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-57-48.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:58:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-58-18.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:58:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-58-49.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:59:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-59-20.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 20:59:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_20-59-50.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:00:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-00-21.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:00:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-00-52.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:01:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-01-22.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:01:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-01-53.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:02:06
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$377
{"id": 39, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:02:10", "screenshot_url": "/screenshots/a-20250503_210210.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$377
{"id": 39, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:02:10", "screenshot_url": "/screenshots/a-20250503_210210.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-02-24.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:02:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-02-54.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:03:06
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-03-25.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$377
{"id": 40, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:03:32", "screenshot_url": "/screenshots/a-20250503_210332.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$377
{"id": 40, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:03:32", "screenshot_url": "/screenshots/a-20250503_210332.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:03:36
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-03-56.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:04:06
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$377
{"id": 41, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:04:25", "screenshot_url": "/screenshots/a-20250503_210425.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$377
{"id": 41, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:04:25", "screenshot_url": "/screenshots/a-20250503_210425.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-04-26.png
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-03 21:04:36
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-03 21:04:36", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-03 21:09:55
$11
last_active
$19
2025-05-03 21:09:55
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-03 21:09:55", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:09:56
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-10-10.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:10:26
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-10-41.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:10:56
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-11-12.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:11:26
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$377
{"id": 42, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:11:32", "screenshot_url": "/screenshots/a-20250503_211132.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$377
{"id": 42, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:11:32", "screenshot_url": "/screenshots/a-20250503_211132.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-11-42.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$377
{"id": 43, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:11:45", "screenshot_url": "/screenshots/a-20250503_211145.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$377
{"id": 43, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:11:45", "screenshot_url": "/screenshots/a-20250503_211145.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:11:56
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$377
{"id": 44, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:11:57", "screenshot_url": "/screenshots/a-20250503_211157.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$377
{"id": 44, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:11:57", "screenshot_url": "/screenshots/a-20250503_211157.png", "ip": "127.0.0.1"}
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$377
{"id": 45, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:12:09", "screenshot_url": "/screenshots/a-20250503_211209.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$377
{"id": 45, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:12:09", "screenshot_url": "/screenshots/a-20250503_211209.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-12-13.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:12:26
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-12-43.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:12:56
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-13-14.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:13:26
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$313
{"id": 46, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://vjudge.net/workbook\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:13:42", "screenshot_url": "/screenshots/a-20250503_211342.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$313
{"id": 46, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://vjudge.net/workbook\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:13:42", "screenshot_url": "/screenshots/a-20250503_211342.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-13-45.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:13:56
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-14-16.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:14:26
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-14-46.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:14:56
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$377
{"id": 47, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:15:16", "screenshot_url": "/screenshots/a-20250503_211516.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$377
{"id": 47, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u25cf chrome_controller.py - supervise3 - Visual Studio Code\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:15:16", "screenshot_url": "/screenshots/a-20250503_211516.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-15-17.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:15:26
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-15-48.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:15:56
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-16-20.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$304
{"id": 48, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://pintia.cn/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:16:21", "screenshot_url": "/screenshots/a-20250503_211621.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$304
{"id": 48, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://pintia.cn/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:16:21", "screenshot_url": "/screenshots/a-20250503_211621.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:16:26
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-03 21:16:42
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-03 21:16:42", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-03 21:18:22
$11
last_active
$19
2025-05-03 21:18:22
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-03 21:18:22", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:18:23
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-18-39.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:18:53
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 49, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:19:01", "screenshot_url": "/screenshots/a-20250503_211901.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$326
{"id": 49, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:19:01", "screenshot_url": "/screenshots/a-20250503_211901.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-19-09.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:19:23
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-19-40.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:19:53
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-20-11.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:20:23
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-20-42.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:20:53
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-21-12.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:21:23
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-21-43.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:21:53
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-22-14.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:22:23
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-22-44.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:22:53
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-23-15.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:23:23
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-23-46.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:23:53
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-03 21:23:59
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-03 21:23:59", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-03 21:24:37
$11
last_active
$19
2025-05-03 21:24:37
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-03 21:24:37", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:24:38
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-24-50.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:25:08
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-25-21.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:25:38
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-25-51.png
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-03 21:26:01
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:26:08
*4
$4
HSET
$17
exam:10:student:1
$6
status
$6
online
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-03 21:26:11
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-03 21:26:11", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-03 21:26:16
$11
last_active
$19
2025-05-03 21:26:16
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-03 21:26:16", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:26:16
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-26-26.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:26:46
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$290
{"id": 50, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u68c0\u6d4b\u5230\u591a\u4e2a\u6807\u7b7e\u9875\uff0c\u8bf7\u5173\u95ed\u591a\u4f59\u6807\u7b7e\u9875", "timestamp": "2025-05-03 21:26:48", "screenshot_url": "/screenshots/a-20250503_212648.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$290
{"id": 50, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u68c0\u6d4b\u5230\u591a\u4e2a\u6807\u7b7e\u9875\uff0c\u8bf7\u5173\u95ed\u591a\u4f59\u6807\u7b7e\u9875", "timestamp": "2025-05-03 21:26:48", "screenshot_url": "/screenshots/a-20250503_212648.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-26-57.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:27:16
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$290
{"id": 51, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u68c0\u6d4b\u5230\u591a\u4e2a\u6807\u7b7e\u9875\uff0c\u8bf7\u5173\u95ed\u591a\u4f59\u6807\u7b7e\u9875", "timestamp": "2025-05-03 21:27:22", "screenshot_url": "/screenshots/a-20250503_212722.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$290
{"id": 51, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u68c0\u6d4b\u5230\u591a\u4e2a\u6807\u7b7e\u9875\uff0c\u8bf7\u5173\u95ed\u591a\u4f59\u6807\u7b7e\u9875", "timestamp": "2025-05-03 21:27:22", "screenshot_url": "/screenshots/a-20250503_212722.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-27-28.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:27:46
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-27-58.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:28:16
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$346
{"id": 52, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u6587\u4ef6\u7ba1\u7406\u5668\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:28:25", "screenshot_url": "/screenshots/a-20250503_212825.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$346
{"id": 52, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u6587\u4ef6\u7ba1\u7406\u5668\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-03 21:28:25", "screenshot_url": "/screenshots/a-20250503_212825.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-28-29.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$290
{"id": 53, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u68c0\u6d4b\u5230\u591a\u4e2a\u6807\u7b7e\u9875\uff0c\u8bf7\u5173\u95ed\u591a\u4f59\u6807\u7b7e\u9875", "timestamp": "2025-05-03 21:28:40", "screenshot_url": "/screenshots/a-20250503_212840.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$290
{"id": 53, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u68c0\u6d4b\u5230\u591a\u4e2a\u6807\u7b7e\u9875\uff0c\u8bf7\u5173\u95ed\u591a\u4f59\u6807\u7b7e\u9875", "timestamp": "2025-05-03 21:28:40", "screenshot_url": "/screenshots/a-20250503_212840.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:28:46
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-03_21-29-00.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-03 21:29:17
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-03 21:29:17
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-03 21:29:17", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 11:39:21
$11
last_active
$19
2025-05-05 11:39:21
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 11:39:21", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 11:39:22
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_11-39-36.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 11:39:52
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_11-40-07.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 11:40:22
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_11-40-38.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 11:40:52
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_11-41-08.png
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 11:41:21
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 11:41:21", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 11:41:29
$11
last_active
$19
2025-05-05 11:41:29
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 11:41:29", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 11:41:30
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_11-41-44.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 11:42:00
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 11:42:12
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 11:42:12", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 11:42:43
$11
last_active
$19
2025-05-05 11:42:43
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 11:42:43", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 11:42:43
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_11-42-56.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 11:43:13
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_11-43-27.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 11:43:43
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_11-43-58.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 11:44:13
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_11-44-28.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 11:44:43
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_11-44-59.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 11:45:13
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_11-45-30.png
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 11:45:35
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 11:45:35", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 12:07:51
$11
last_active
$19
2025-05-05 12:07:51
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 12:07:51", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:07:52
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-08-07.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:08:22
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-08-37.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:08:52
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-09-08.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:09:22
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$303
{"id": 54, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: http://baidu.com/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:09:29", "screenshot_url": "/screenshots/a-20250505_120929.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$303
{"id": 54, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: http://baidu.com/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:09:29", "screenshot_url": "/screenshots/a-20250505_120929.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-09-38.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:09:52
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$305
{"id": 55, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://vjudge.net/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:10:06", "screenshot_url": "/screenshots/a-20250505_121006.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$305
{"id": 55, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://vjudge.net/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:10:06", "screenshot_url": "/screenshots/a-20250505_121006.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-10-09.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:10:22
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-10-39.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:10:52
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-11-10.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:11:22
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-11-40.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:11:52
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-12-10.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:12:22
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-12-41.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:12:52
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 12:13:07
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 12:13:07", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 12:14:14
$11
last_active
$19
2025-05-05 12:14:14
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 12:14:14", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:14:15
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-14-28.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:14:45
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 12:14:49
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 12:14:49", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 12:15:30
$11
last_active
$19
2025-05-05 12:15:30
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 12:15:30", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:15:31
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-15-46.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:16:01
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-16-16.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:16:31
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-16-47.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:17:01
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-17-17.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:17:31
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-17-48.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:18:01
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$303
{"id": 56, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: http://baidu.com/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:18:10", "screenshot_url": "/screenshots/a-20250505_121810.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$303
{"id": 56, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: http://baidu.com/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:18:10", "screenshot_url": "/screenshots/a-20250505_121810.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-18-18.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:18:31
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-18-48.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$303
{"id": 57, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: http://baidu.com/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:18:53", "screenshot_url": "/screenshots/a-20250505_121853.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$303
{"id": 57, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: http://baidu.com/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:18:53", "screenshot_url": "/screenshots/a-20250505_121853.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:19:01
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$314
{"id": 58, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://www.doubao.com/chat/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:19:09", "screenshot_url": "/screenshots/a-20250505_121909.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$314
{"id": 58, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://www.doubao.com/chat/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:19:09", "screenshot_url": "/screenshots/a-20250505_121909.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-19-19.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:19:31
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 12:19:45
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 12:19:45", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 12:24:20
$11
last_active
$19
2025-05-05 12:24:20
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 12:24:20", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:24:20
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-24-34.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:24:51
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-25-05.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:25:21
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$322
{"id": 59, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: Weixin\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:25:28", "screenshot_url": "/screenshots/a-20250505_122528.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$322
{"id": 59, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: Weixin\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:25:28", "screenshot_url": "/screenshots/a-20250505_122528.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-25-35.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:25:51
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-26-05.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:26:21
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$324
{"id": 60, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://vjudge.net/problem/Gym-105629A\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:26:29", "screenshot_url": "/screenshots/a-20250505_122629.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$324
{"id": 60, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://vjudge.net/problem/Gym-105629A\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:26:29", "screenshot_url": "/screenshots/a-20250505_122629.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-26-36.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:26:51
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-27-06.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:27:21
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-27-37.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:27:51
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$322
{"id": 61, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: Weixin\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:27:54", "screenshot_url": "/screenshots/a-20250505_122754.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$322
{"id": 61, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: Weixin\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:27:54", "screenshot_url": "/screenshots/a-20250505_122754.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-28-07.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:28:21
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-28-38.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:28:51
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-29-08.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:29:21
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-29-39.png
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 12:29:48
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 12:29:48", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 12:47:07
$11
last_active
$19
2025-05-05 12:47:07
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 12:47:07", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:47:07
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-47-22.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:47:37
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-47-52.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:48:07
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-48-23.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:48:37
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-48-53.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:49:07
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-49-23.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:49:37
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-49-54.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:50:07
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-50-24.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:50:37
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-50-55.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:51:07
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-51-25.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:51:37
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-51-56.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:52:09
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-52-28.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:52:39
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_12-52-59.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 12:53:09
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 62, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:53:16", "screenshot_url": "/screenshots/a-20250505_125316.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$326
{"id": 62, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 12:53:16", "screenshot_url": "/screenshots/a-20250505_125316.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 12:53:23
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 12:53:23", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 17:28:58
$11
last_active
$19
2025-05-05 17:28:58
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 17:28:58", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:28:58
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-29-13.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:29:28
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-29-43.png
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 17:29:56
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 17:29:56", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 17:30:50
$11
last_active
$19
2025-05-05 17:30:50
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 17:30:50", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:30:51
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-31-04.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:31:21
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-31-35.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:31:51
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-32-06.png
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 17:32:11
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 17:32:11", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 17:32:55
$11
last_active
$19
2025-05-05 17:32:55
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 17:32:55", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:32:55
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-33-13.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:33:25
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-33-43.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:33:55
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 17:34:13
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 17:34:13", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 17:35:21
$11
last_active
$19
2025-05-05 17:35:21
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 17:35:21", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:35:21
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-35-35.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:35:51
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-36-06.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$308
{"id": 63, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://www.baidu.com/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 17:36:12", "screenshot_url": "/screenshots/a-20250505_173612.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$308
{"id": 63, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://www.baidu.com/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 17:36:12", "screenshot_url": "/screenshots/a-20250505_173612.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:36:21
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-36-37.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$306
{"id": 64, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://cn.bing.com/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 17:36:40", "screenshot_url": "/screenshots/a-20250505_173640.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$306
{"id": 64, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://cn.bing.com/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 17:36:40", "screenshot_url": "/screenshots/a-20250505_173640.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:36:51
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 17:36:54
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 17:36:54", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 17:41:41
$11
last_active
$19
2025-05-05 17:41:41
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 17:41:41", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:41:41
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:42:12
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-42-15.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:42:42
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-42-45.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$308
{"id": 65, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://www.baidu.com/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 17:43:11", "screenshot_url": "/screenshots/a-20250505_174311.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$308
{"id": 65, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://www.baidu.com/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 17:43:11", "screenshot_url": "/screenshots/a-20250505_174311.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:43:12
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-43-15.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:43:42
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-43-46.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:44:12
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-44-17.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:44:42
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_17-44-47.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$322
{"id": 66, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: Weixin\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 17:45:07", "screenshot_url": "/screenshots/a-20250505_174507.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$322
{"id": 66, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: Weixin\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 17:45:07", "screenshot_url": "/screenshots/a-20250505_174507.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 17:45:12
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 17:45:16
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 17:45:16", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 22:44:08
$11
last_active
$19
2025-05-05 22:44:08
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 22:44:08", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:44:09
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:44:39
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_22-44-53.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:45:09
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 22:45:20
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 22:45:20", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 22:49:08
$11
last_active
$19
2025-05-05 22:49:08
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 22:49:08", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:49:08
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:49:38
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_22-49-44.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:50:08
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 22:50:11
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 22:50:11", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 22:52:06
$11
last_active
$19
2025-05-05 22:52:06
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 22:52:06", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:52:07
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:52:37
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_22-52-48.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:53:07
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_22-53-18.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:53:37
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_22-53-49.png
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 22:53:57
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 22:53:57", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 22:54:07
$11
last_active
$19
2025-05-05 22:54:07
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 22:54:07", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:54:08
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:54:38
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_22-54-57.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:55:08
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_22-55-27.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:55:38
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_22-55-58.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:56:08
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 22:56:28
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 22:56:28", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 22:57:00
$11
last_active
$19
2025-05-05 22:57:00
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 22:57:00", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:57:00
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_22-57-10.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:57:30
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_22-57-41.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:58:00
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_22-58-12.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:58:30
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_22-58-42.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 22:59:00
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 22:59:13
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 22:59:13", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 23:01:22
$11
last_active
$19
2025-05-05 23:01:22
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 23:01:22", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:01:23
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:01:53
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-02-00.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:02:23
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-02-31.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:02:53
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-03-01.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:03:23
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-03-32.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:03:53
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-04-02.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:04:23
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-04-33.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:04:53
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-05-03.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:05:23
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-05-34.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:05:53
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-06-04.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:06:23
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-06-35.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:06:53
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-07-05.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:07:23
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 23:07:25
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 23:07:25", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 23:11:19
$11
last_active
$19
2025-05-05 23:11:19
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 23:11:19", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:11:20
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:11:50
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:12:20
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:12:50
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:13:20
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:13:50
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:14:20
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:14:50
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-15-04.png
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 23:15:08
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 23:15:08", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 23:15:18
$11
last_active
$19
2025-05-05 23:15:18
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 23:15:18", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:15:19
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:15:49
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-15-58.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:16:19
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-16-28.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:16:49
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-16-59.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:17:19
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-17-29.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:17:49
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-18-00.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:18:19
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-18-31.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:18:49
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-19-01.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:19:19
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-19-32.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:19:49
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-20-02.png
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 23:20:05
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:20:19
*4
$4
HSET
$17
exam:10:student:1
$6
status
$6
online
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-20-33.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:20:49
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-21-03.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:21:19
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-21-34.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:21:49
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-22-04.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:22:19
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-22-35.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:22:49
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-23-05.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:23:19
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-23-36.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$314
{"id": 67, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://www.doubao.com/chat/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 23:23:37", "screenshot_url": "/screenshots/a-20250505_232337.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$314
{"id": 67, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://www.doubao.com/chat/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 23:23:37", "screenshot_url": "/screenshots/a-20250505_232337.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:23:49
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-24-07.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:24:19
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-24-37.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:24:49
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$314
{"id": 68, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://www.doubao.com/chat/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 23:25:05", "screenshot_url": "/screenshots/a-20250505_232505.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$314
{"id": 68, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://www.doubao.com/chat/\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 23:25:05", "screenshot_url": "/screenshots/a-20250505_232505.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-25-08.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:25:19
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-25-38.png
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 23:25:41
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 23:25:41", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 23:32:46
$11
last_active
$19
2025-05-05 23:32:46
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 23:32:46", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:32:46
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:33:17
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-33-22.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:33:47
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-33-52.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:34:17
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-34-23.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:34:47
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-34-54.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:35:17
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-35-25.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:35:47
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-35-55.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$329
{"id": 69, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://news.qq.com/rain/a/20250505A071ND00\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 23:35:56", "screenshot_url": "/screenshots/a-20250505_233556.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$329
{"id": 69, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://news.qq.com/rain/a/20250505A071ND00\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 23:35:56", "screenshot_url": "/screenshots/a-20250505_233556.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:36:17
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-36-26.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:36:47
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-36-56.png
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 23:37:10
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 23:37:10", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 23:44:27
$11
last_active
$19
2025-05-05 23:44:27
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 23:44:27", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:44:28
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-44-36.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:44:58
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-45-06.png
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 23:45:18
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:45:28
*4
$4
HSET
$17
exam:10:student:1
$6
status
$6
online
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-45-37.png
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 23:45:40
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 23:45:40", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 23:45:46
$11
last_active
$19
2025-05-05 23:45:46
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-05 23:45:46", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:45:46
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-46-05.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:46:16
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 70, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 23:46:28", "screenshot_url": "/screenshots/a-20250505_234628.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$326
{"id": 70, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 23:46:28", "screenshot_url": "/screenshots/a-20250505_234628.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-46-36.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:46:46
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-47-06.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:47:16
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-47-37.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$338
{"id": 71, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://baijiahao.baidu.com/s?id=1831268900994613354\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 23:47:43", "screenshot_url": "/screenshots/a-20250505_234743.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$338
{"id": 71, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://baijiahao.baidu.com/s?id=1831268900994613354\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 23:47:43", "screenshot_url": "/screenshots/a-20250505_234743.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:47:46
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-48-08.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:48:16
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-48-38.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:48:46
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-49-09.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:49:16
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-49-39.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:49:46
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-50-10.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:50:16
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$324
{"id": 72, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://vjudge.net/problem/Gym-105629A\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 23:50:29", "screenshot_url": "/screenshots/a-20250505_235029.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$324
{"id": 72, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://vjudge.net/problem/Gym-105629A\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 23:50:29", "screenshot_url": "/screenshots/a-20250505_235029.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-50-40.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:50:46
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-51-11.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:51:16
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$589
{"id": 73, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://image.baidu.com/search/index?tn=baiduimage&ct=201326592&lm=-1&cl=2&ie=utf8&fr=ala&ala=1&alatpl=normal&pos=3&dyTabStr=MCwzLDEsMiwxMyw3LDYsNSwxMiw5&word=%E7%94%B7%E5%AD%90%E5%87%BA%E6%B5%B7%E6%89%93%E6%8D%9E%E4%B8%8A%E6%9D%A5%E7%9A%84%E7%94%9F%E8%9A%9D%E5%B8%A6%E5%95%86%E5%93%81%E6%A0%87%E7%AD%BE\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 23:51:23", "screenshot_url": "/screenshots/a-20250505_235123.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$589
{"id": 73, "student_id": "1", "exam_id": "10", "username": "a", "reason": "\u672a\u6388\u6743\u7684URL: https://image.baidu.com/search/index?tn=baiduimage&ct=201326592&lm=-1&cl=2&ie=utf8&fr=ala&ala=1&alatpl=normal&pos=3&dyTabStr=MCwzLDEsMiwxMyw3LDYsNSwxMiw5&word=%E7%94%B7%E5%AD%90%E5%87%BA%E6%B5%B7%E6%89%93%E6%8D%9E%E4%B8%8A%E6%9D%A5%E7%9A%84%E7%94%9F%E8%9A%9D%E5%B8%A6%E5%95%86%E5%93%81%E6%A0%87%E7%AD%BE\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684URL\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-05 23:51:23", "screenshot_url": "/screenshots/a-20250505_235123.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-51-42.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:51:46
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-52-13.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:52:16
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-52-43.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:52:46
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-05_23-53-14.png
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 23:53:16
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 23:53:16", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-05 23:53:16
*4
$4
HSET
$17
exam:10:student:1
$6
status
$6
online
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$11
logout_time
$19
2025-05-05 23:58:16
*3
$5
RPUSH
$24
exam:10:student:1:logins
$73
{"type": "logout", "timestamp": "2025-05-05 23:58:16", "ip": "127.0.0.1"}
*3
$6
INCRBY
$26
exam:10:student_id_counter
$1
1
*12
$5
HMSET
$17
exam:10:student:2
$2
id
$1
2
$8
username
$1
b
$7
exam_id
$2
10
$6
status
$7
offline
$10
created_at
$19
2025-05-05 23:59:17
*3
$6
INCRBY
$26
exam:10:student_id_counter
$1
1
*12
$5
HMSET
$17
exam:10:student:3
$2
id
$1
3
$8
username
$1
c
$7
exam_id
$2
10
$6
status
$7
offline
$10
created_at
$19
2025-05-05 23:59:17
*3
$6
INCRBY
$26
exam:10:student_id_counter
$1
1
*12
$5
HMSET
$17
exam:10:student:4
$2
id
$1
4
$8
username
$1
d
$7
exam_id
$2
10
$6
status
$7
offline
$10
created_at
$19
2025-05-05 23:59:17
*3
$6
INCRBY
$26
exam:10:student_id_counter
$1
1
*12
$5
HMSET
$17
exam:10:student:5
$2
id
$1
5
$8
username
$1
e
$7
exam_id
$2
10
$6
status
$7
offline
$10
created_at
$19
2025-05-05 23:59:17
*3
$6
INCRBY
$26
exam:10:student_id_counter
$1
1
*12
$5
HMSET
$17
exam:10:student:6
$2
id
$1
6
$8
username
$1
f
$7
exam_id
$2
10
$6
status
$7
offline
$10
created_at
$19
2025-05-05 23:59:17
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:5
$2
id
$1
5
$8
username
$1
e
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-05 23:59:34
$11
last_active
$19
2025-05-05 23:59:34
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:5:logins
$72
{"type": "login", "timestamp": "2025-05-05 23:59:34", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:5
$11
last_active
$19
2025-05-05 23:59:34
*3
$5
RPUSH
$29
exam:10:student:5:screenshots
$39
screenshot_5_10_2025-05-05_23-59-50.png
*4
$4
HSET
$17
exam:10:student:5
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:5
$11
logout_time
$19
2025-05-06 00:00:04
*3
$5
RPUSH
$24
exam:10:student:5:logins
$73
{"type": "logout", "timestamp": "2025-05-06 00:00:04", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:3
$2
id
$1
3
$8
username
$1
c
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-06 00:00:50
$11
last_active
$19
2025-05-06 00:00:50
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:3:logins
$72
{"type": "login", "timestamp": "2025-05-06 00:00:50", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:3
$11
last_active
$19
2025-05-06 00:00:50
*3
$5
RPUSH
$29
exam:10:student:3:screenshots
$39
screenshot_3_10_2025-05-06_00-01-08.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 74, "student_id": "3", "exam_id": "10", "username": "c", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-06 00:01:09", "screenshot_url": "/screenshots/c-20250506_000109.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$326
{"id": 74, "student_id": "3", "exam_id": "10", "username": "c", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-06 00:01:09", "screenshot_url": "/screenshots/c-20250506_000109.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:3
$11
last_active
$19
2025-05-06 00:01:20
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:6
$2
id
$1
6
$8
username
$1
f
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-06 00:01:30
$11
last_active
$19
2025-05-06 00:01:30
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:6:logins
$72
{"type": "login", "timestamp": "2025-05-06 00:01:30", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:6
$11
last_active
$19
2025-05-06 00:01:31
*3
$5
RPUSH
$29
exam:10:student:3:screenshots
$39
screenshot_3_10_2025-05-06_00-01-38.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$326
{"id": 75, "student_id": "3", "exam_id": "10", "username": "c", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-06 00:01:42", "screenshot_url": "/screenshots/c-20250506_000142.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$326
{"id": 75, "student_id": "3", "exam_id": "10", "username": "c", "reason": "\u672a\u53d7\u63a7\u7684Chrome\u6d4f\u89c8\u5668\uff0c\u8bf7\u5207\u6362\u5230\u5141\u8bb8\u7684\u6d4f\u89c8\u5668\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-06 00:01:42", "screenshot_url": "/screenshots/c-20250506_000142.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:6:screenshots
$39
screenshot_6_10_2025-05-06_00-01-45.png
*4
$4
HSET
$17
exam:10:student:3
$11
last_active
$19
2025-05-06 00:01:50
*4
$4
HSET
$17
exam:10:student:3
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:3
$11
logout_time
$19
2025-05-06 00:01:55
*3
$5
RPUSH
$24
exam:10:student:3:logins
$73
{"type": "logout", "timestamp": "2025-05-06 00:01:55", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:6
$11
last_active
$19
2025-05-06 00:02:01
*3
$5
RPUSH
$29
exam:10:student:6:screenshots
$39
screenshot_6_10_2025-05-06_00-02-15.png
*4
$4
HSET
$17
exam:10:student:6
$11
last_active
$19
2025-05-06 00:02:31
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$335
{"id": 76, "student_id": "6", "exam_id": "10", "username": "f", "reason": "\u83b7\u53d6\u6807\u7b7e\u9875\u53e5\u67c4\u65f6\u51fa\u9519: cannot access local variable 'current_handle' where it is not associated with a value", "timestamp": "2025-05-06 00:02:36", "screenshot_url": "/screenshots/f-20250506_000236.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$335
{"id": 76, "student_id": "6", "exam_id": "10", "username": "f", "reason": "\u83b7\u53d6\u6807\u7b7e\u9875\u53e5\u67c4\u65f6\u51fa\u9519: cannot access local variable 'current_handle' where it is not associated with a value", "timestamp": "2025-05-06 00:02:36", "screenshot_url": "/screenshots/f-20250506_000236.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:6:screenshots
$39
screenshot_6_10_2025-05-06_00-02-45.png
*3
$6
INCRBY
$20
violation_id_counter
$1
1
*3
$5
RPUSH
$10
violations
$346
{"id": 77, "student_id": "6", "exam_id": "10", "username": "f", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u4efb\u52a1\u7ba1\u7406\u5668\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-06 00:02:48", "screenshot_url": "/screenshots/f-20250506_000248.png", "ip": "127.0.0.1"}
*3
$5
RPUSH
$18
exam:10:violations
$346
{"id": 77, "student_id": "6", "exam_id": "10", "username": "f", "reason": "\u672a\u6388\u6743\u7684\u524d\u53f0\u5e94\u7528: \u4efb\u52a1\u7ba1\u7406\u5668\uff0c\u5207\u6362\u5230\u5141\u8bb8\u7684\u5e94\u7528\u8fdb\u884c\u8003\u8bd5", "timestamp": "2025-05-06 00:02:48", "screenshot_url": "/screenshots/f-20250506_000248.png", "ip": "127.0.0.1"}
*4
$4
HSET
$17
exam:10:student:6
$11
last_active
$19
2025-05-06 00:03:01
*4
$4
HSET
$17
exam:10:student:6
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:6
$11
logout_time
$19
2025-05-06 00:04:06
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:1
$2
id
$1
1
$8
username
$1
a
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-06 00:41:45
$11
last_active
$19
2025-05-06 00:41:45
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:1:logins
$72
{"type": "login", "timestamp": "2025-05-06 00:41:45", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-06_00-42-03.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-06 00:42:03
*4
$4
HSET
$17
exam:10:student:1
$6
status
$6
online
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-06_00-42-34.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-06 00:42:34
*4
$4
HSET
$17
exam:10:student:1
$6
status
$6
online
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-06_00-43-04.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-06 00:43:04
*4
$4
HSET
$17
exam:10:student:1
$6
status
$6
online
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-06_00-43-36.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-06 00:43:36
*4
$4
HSET
$17
exam:10:student:1
$6
status
$6
online
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-06_00-44-06.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-06 00:44:06
*4
$4
HSET
$17
exam:10:student:1
$6
status
$6
online
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-06_00-44-37.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-06 00:44:37
*4
$4
HSET
$17
exam:10:student:1
$6
status
$6
online
*3
$5
RPUSH
$29
exam:10:student:1:screenshots
$39
screenshot_1_10_2025-05-06_00-45-07.png
*4
$4
HSET
$17
exam:10:student:1
$11
last_active
$19
2025-05-06 00:45:07
*4
$4
HSET
$17
exam:10:student:1
$6
status
$6
online
*4
$4
HSET
$17
exam:10:student:1
$6
status
$7
offline
*4
$4
HSET
$17
exam:10:student:1
$12
offline_time
$19
2025-05-06 00:47:22
*3
$5
RPUSH
$24
exam:10:student:1:logins
$74
{"type": "offline", "timestamp": "2025-05-06 00:47:22", "ip": "127.0.0.1"}
*1
$5
MULTI
*16
$5
HMSET
$17
exam:10:student:2
$2
id
$1
2
$8
username
$1
b
$2
ip
$9
127.0.0.1
$7
exam_id
$2
10
$10
login_time
$19
2025-05-06 00:50:19
$11
last_active
$19
2025-05-06 00:50:19
$6
status
$6
online
*1
$4
EXEC
*3
$5
RPUSH
$24
exam:10:student:2:logins
$72
{"type": "login", "timestamp": "2025-05-06 00:50:19", "ip": "127.0.0.1"}
*3
$5
RPUSH
$29
exam:10:student:2:screenshots
$39
screenshot_2_10_2025-05-06_00-50-35.png
*4
$4
HSET
$17
exam:10:student:2
$11
last_active
$19
2025-05-06 00:50:35
*4
$4
HSET
$17
exam:10:student:2
$6
status
$6
online
*4
$4
HSET
$17
exam:10:student:2
$6
status
$6
logout
*4
$4
HSET
$17
exam:10:student:2
$11
logout_time
$19
2025-05-06 00:50:56
*3
$5
RPUSH
$24
exam:10:student:2:logins
$73
{"type": "logout", "timestamp": "2025-05-06 00:50:56", "ip": "127.0.0.1"}
*3
$4
HDEL
$12
exam_configs
$1
8
*8
$3
DEL
$23
exam:8:student:2:logins
$28
exam:8:student:1:screenshots
$16
exam:8:student:3
$23
exam:8:student:1:logins
$16
exam:8:student:1
$28
exam:8:student:2:screenshots
$16
exam:8:student:2
*2
$3
DEL
$17
exam:8:violations
*2
$3
DEL
$25
exam:8:student_id_counter
*3
$4
HDEL
$12
exam_configs
$1
9
*29
$3
DEL
$24
exam:9:student:22:logins
$17
exam:9:student:22
$16
exam:9:student:3
$17
exam:9:student:11
$16
exam:9:student:5
$24
exam:9:student:21:logins
$29
exam:9:student:21:screenshots
$16
exam:9:student:6
$17
exam:9:student:16
$17
exam:9:student:12
$17
exam:9:student:18
$17
exam:9:student:13
$28
exam:9:student:3:screenshots
$17
exam:9:student:17
$29
exam:9:student:22:screenshots
$17
exam:9:student:14
$16
exam:9:student:1
$17
exam:9:student:20
$17
exam:9:student:19
$16
exam:9:student:9
$17
exam:9:student:10
$17
exam:9:student:15
$16
exam:9:student:4
$16
exam:9:student:2
$16
exam:9:student:8
$17
exam:9:student:21
$23
exam:9:student:3:logins
$16
exam:9:student:7
*2
$3
DEL
$17
exam:9:violations
*2
$3
DEL
$25
exam:9:student_id_counter
*3
$6
INCRBY
$15
exam_id_counter
$1
1
*4
$4
HSET
$12
exam_configs
$2
11
$163
{"id": 11, "name": "b", "start_time": "2025-05-06T08:04", "end_time": "2025-05-06T08:06", "created_at": "2025-05-06 08:04:15", "status": "pending", "delay_min": 0}
