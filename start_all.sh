#!/bin/bash
echo "启动考试监控系统..."

# 启动状态检查器
echo "启动状态检查器..."
python3 status_checker.py &
STATUS_CHECKER_PID=$!
echo "状态检查器 PID: $STATUS_CHECKER_PID"

# 启动Web服务器
echo "启动Web服务器..."
gunicorn -c gunicorn_config.py server:app &
WEB_SERVER_PID=$!
echo "Web服务器 PID: $WEB_SERVER_PID"

# 保存PID到文件
echo $STATUS_CHECKER_PID > status_checker.pid
echo $WEB_SERVER_PID > web_server.pid

echo "所有服务已启动"
echo "状态检查器: $STATUS_CHECKER_PID"
echo "Web服务器: $WEB_SERVER_PID"

# 等待服务
wait $STATUS_CHECKER_PID $WEB_SERVER_PID