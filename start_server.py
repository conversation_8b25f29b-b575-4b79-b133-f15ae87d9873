#!/usr/bin/env python3
"""
启动脚本 - 确保 MergeManager 在 Gunicorn 环境下正常工作
"""

import os
import sys
import logging
from server import create_app

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('server.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger("StartServer")
    
    logger.info(f"启动服务器，进程ID: {os.getpid()}")
    
    # 创建应用实例
    app = create_app()
    
    # 确保 MergeManager 已初始化
    logger.info(f"MergeManager 实例: {app.merge_manager}")
    logger.info(f"MergeManager 队列大小: {len(app.merge_manager.task_queue)}")
    
    if len(sys.argv) > 1 and sys.argv[1] == 'gunicorn':
        # 通过 Gunicorn 启动
        logger.info("通过 Gunicorn 启动")
        return app
    else:
        # 直接启动
        logger.info("直接启动 Flask 开发服务器")
        app.run(host='0.0.0.0', port=5000, debug=True)

if __name__ == '__main__':
    main()
