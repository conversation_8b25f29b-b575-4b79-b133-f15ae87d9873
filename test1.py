import cv2
import numpy as np
import pyautogui
import time
import threading
from datetime import datetime

class ScreenRecorder:
    def __init__(self, output_path="screen_recording.mp4"):
        self.output_path = output_path
        self.is_recording = False
        self.writer = None
        self.fps = 30
        self.quality_settings = {
            'codec': 'mp4v',
            'compression': 0,  # 0-100, 0为最高质量
            'bitrate': None,
            'crf': None
        }
        
    def set_quality_preset(self, preset="high"):
        """设置预设质量等级"""
        presets = {
            "ultra": {
                'codec': 'mp4v',
                'fps': 60,
                'compression': 0,
                'description': "最高质量，大文件"
            },
            "high": {
                'codec': 'avc1', 
                'fps': 30,
                'compression': 10,
                'description': "高质量，平衡文件大小"
            },
            "medium": {
                'codec': 'XVID',
                'fps': 24,
                'compression': 30,
                'description': "中等质量，中等文件大小"
            },
            "low": {
                'codec': 'XVID',
                'fps': 15,
                'compression': 50,
                'description': "低质量，小文件"
            }
        }
        
        if preset in presets:
            settings = presets[preset]
            self.quality_settings['codec'] = settings['codec']
            self.fps = settings['fps']
            self.quality_settings['compression'] = settings['compression']
            print(f"质量预设: {preset} - {settings['description']}")
        else:
            print(f"未知预设: {preset}")
    
    def set_custom_quality(self, codec='mp4v', fps=30, compression=0, bitrate=None):
        """自定义质量设置"""
        self.quality_settings['codec'] = codec
        self.fps = fps
        self.quality_settings['compression'] = compression
        self.quality_settings['bitrate'] = bitrate
        
        print(f"自定义设置:")
        print(f"  编码器: {codec}")
        print(f"  帧率: {fps}")
        print(f"  压缩率: {compression}")
        print(f"  比特率: {bitrate}")
    
    def create_writer(self, width, height):
        """创建视频写入器"""
        # 获取编码器
        codec = self.quality_settings['codec']
        fourcc = cv2.VideoWriter_fourcc(*codec)
        
        # 创建写入器
        self.writer = cv2.VideoWriter(
            self.output_path,
            fourcc,
            self.fps,
            (width, height)
        )
        
        # 设置质量参数（如果支持）
        if self.writer.isOpened():
            try:
                # 设置压缩质量（0-100，越小质量越高）
                compression = self.quality_settings['compression']
                self.writer.set(cv2.VIDEOWRITER_PROP_QUALITY, 100 - compression)
                
                # 设置比特率（如果支持）
                if self.quality_settings['bitrate']:
                    self.writer.set(cv2.VIDEOWRITER_PROP_BITRATE, self.quality_settings['bitrate'])
                    
                print(f"录制参数:")
                print(f"  输出文件: {self.output_path}")
                print(f"  分辨率: {width}x{height}")
                print(f"  帧率: {self.fps}")
                print(f"  编码器: {codec}")
                print(f"  压缩率: {compression}%")
                
            except Exception as e:
                print(f"设置质量参数时出错: {e}")
                
        return self.writer.isOpened()
    
    def start_recording(self, region=None, duration=None):
        """开始录制"""
        if self.is_recording:
            print("已在录制中...")
            return
        
        # 获取屏幕分辨率
        if region:
            x, y, width, height = region
        else:
            screen_size = pyautogui.size()
            x, y = 0, 0
            width, height = screen_size.width, screen_size.height
        
        # 确保分辨率是偶数（某些编码器要求）
        width = width if width % 2 == 0 else width - 1
        height = height if height % 2 == 0 else height - 1
        
        # 创建写入器
        if not self.create_writer(width, height):
            print("无法创建视频写入器")
            return
        
        self.is_recording = True
        print(f"开始录制... 按 Ctrl+C 停止")
        
        start_time = time.time()
        frame_count = 0
        
        try:
            while self.is_recording:
                # 截取屏幕
                screenshot = pyautogui.screenshot(region=(x, y, width, height))
                
                # 转换为OpenCV格式
                frame = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
                
                # 写入帧
                self.writer.write(frame)
                frame_count += 1
                
                # 控制帧率
                elapsed_time = time.time() - start_time
                expected_frames = int(elapsed_time * self.fps)
                if frame_count > expected_frames:
                    time.sleep(1.0 / self.fps)
                
                # 检查录制时长
                if duration and elapsed_time >= duration:
                    break
                    
        except KeyboardInterrupt:
            print("\n录制被中断")
        
        self.stop_recording()
        
        # 显示录制统计
        total_time = time.time() - start_time
        actual_fps = frame_count / total_time if total_time > 0 else 0
        print(f"录制完成:")
        print(f"  总时长: {total_time:.2f}秒")
        print(f"  总帧数: {frame_count}")
        print(f"  平均帧率: {actual_fps:.2f}")
    
    def stop_recording(self):
        """停止录制"""
        if self.is_recording:
            self.is_recording = False
            if self.writer:
                self.writer.release()
                self.writer = None
            print(f"录制已停止，文件保存为: {self.output_path}")

class AdvancedScreenRecorder(ScreenRecorder):
    """高级录屏器，支持更多质量设置"""
    
    def __init__(self, output_path="advanced_recording.mp4"):
        super().__init__(output_path)
        self.compression_params = []
    
    def set_h264_quality(self, crf=23, preset="medium"):
        """设置H.264质量参数"""
        # CRF: 0-51, 越小质量越高
        # preset: ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow
        
        self.quality_settings['codec'] = 'H264'
        self.quality_settings['crf'] = crf
        self.quality_settings['preset'] = preset
        
        print(f"H.264设置:")
        print(f"  CRF: {crf} (0-51, 越小质量越高)")
        print(f"  预设: {preset}")
    
    def set_compression_params(self, **params):
        """设置压缩参数"""
        # 常用参数
        compression_options = {
            'VIDEOWRITER_PROP_QUALITY': 'quality',  # 0-100
            'VIDEOWRITER_PROP_FRAMEBYTES': 'frame_bytes',  # 每帧字节数
        }
        
        for key, value in params.items():
            if key in compression_options:
                self.compression_params.append((getattr(cv2, key), value))
                print(f"设置 {compression_options[key]}: {value}")
    
    def create_writer_with_params(self, width, height):
        """创建带参数的写入器"""
        # 尝试不同的编码器
        codecs_to_try = [
            ('mp4v', cv2.VideoWriter_fourcc(*'mp4v')),
            ('XVID', cv2.VideoWriter_fourcc(*'XVID')),
            ('MJPG', cv2.VideoWriter_fourcc(*'MJPG')),
            ('H264', cv2.VideoWriter_fourcc(*'H264')),
        ]
        
        for codec_name, fourcc in codecs_to_try:
            try:
                self.writer = cv2.VideoWriter(
                    self.output_path,
                    fourcc,
                    self.fps,
                    (width, height)
                )
                
                if self.writer.isOpened():
                    print(f"成功创建写入器，编码器: {codec_name}")
                    
                    # 应用压缩参数
                    for param, value in self.compression_params:
                        try:
                            self.writer.set(param, value)
                        except Exception as e:
                            print(f"设置参数 {param} 失败: {e}")
                    
                    return True
                else:
                    self.writer = None
                    
            except Exception as e:
                print(f"编码器 {codec_name} 创建失败: {e}")
                continue
        
        return False

def demonstrate_quality_settings():
    """演示不同质量设置"""
    
    print("=== OpenCV录屏质量设置演示 ===\n")
    
    # 基本录屏器
    recorder = ScreenRecorder("basic_recording.mp4")
    
    # 1. 预设质量
    print("1. 预设质量等级:")
    recorder.set_quality_preset("high")
    
    # 2. 自定义质量
    print("\n2. 自定义质量设置:")
    recorder.set_custom_quality(
        codec='mp4v',
        fps=30,
        compression=15,  # 15%压缩
        bitrate=5000000  # 5Mbps
    )
    
    # 3. 高级设置
    print("\n3. 高级质量设置:")
    advanced_recorder = AdvancedScreenRecorder("advanced_recording.mp4")
    advanced_recorder.set_h264_quality(crf=18, preset="slow")
    advanced_recorder.set_compression_params(
        VIDEOWRITER_PROP_QUALITY=95,
        VIDEOWRITER_PROP_BITRATE=8000000
    )
    
    # 4. 不同编码器比较
    print("\n4. 编码器质量比较:")
    codecs = [
        ('mp4v', '通用MP4编码器'),
        ('XVID', 'XVID编码器，兼容性好'),
        ('MJPG', 'Motion JPEG，质量高但文件大'),
        ('H264', 'H.264编码器，效率高')
    ]
    
    for codec, description in codecs:
        print(f"  {codec}: {description}")

def quality_test_recording():
    """质量测试录制"""
    print("\n=== 开始5秒钟的质量测试录制 ===")
    
    recorder = ScreenRecorder("quality_test.mp4")
    recorder.set_quality_preset("high")
    
    # 录制5秒钟
    recorder.start_recording(duration=5)

if __name__ == "__main__":
    # 演示质量设置
    #demonstrate_quality_settings()
    
    # 进行测试录制
    response = input("\n是否进行5秒钟的测试录制? (y/n): ")
    if response.lower() == 'y':
        quality_test_recording()
    