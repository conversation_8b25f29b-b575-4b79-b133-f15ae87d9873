#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同编码器生成的视频文件在浏览器中的兼容性
"""

import cv2
import numpy as np
import os
import subprocess
import shutil

def test_codec_browser_compatibility():
    """测试不同编码器的浏览器兼容性"""
    print("浏览器视频兼容性测试")
    print("=" * 50)
    
    # 测试参数
    width, height = 1280, 720
    fps = 10
    duration = 3  # 3秒测试视频
    
    # 编码器列表（按浏览器兼容性排序）
    codecs = [
        ('H264', '.mp4', 'H.264/AVC - 最佳浏览器兼容性'),
        ('mp4v', '.mp4', 'MPEG-4 Part 2 - 有限浏览器支持'),
        ('VP80', '.webm', 'VP8 - WebM格式，Chrome/Firefox支持'),
        ('VP90', '.webm', 'VP9 - WebM格式，现代浏览器支持'),
        ('XVID', '.avi', 'XVID - AVI格式，需要插件'),
        ('MJPG', '.avi', 'Motion JPEG - 基础兼容性')
    ]
    
    results = []
    
    for codec, ext, description in codecs:
        print(f"\n测试编码器: {codec} ({description})")
        print("-" * 40)
        
        # 创建测试文件名
        test_file = f"test_{codec}{ext}"
        
        try:
            # 测试编码器可用性
            fourcc = cv2.VideoWriter_fourcc(*codec)
            writer = cv2.VideoWriter(test_file, fourcc, fps, (width, height))
            
            if writer.isOpened():
                print(f"✓ 编码器可用")
                
                # 生成测试视频
                for i in range(fps * duration):
                    # 创建彩色测试帧
                    frame = np.zeros((height, width, 3), dtype=np.uint8)
                    
                    # 添加动态内容
                    color_value = (i * 25) % 256
                    frame[:, :, 0] = color_value  # 蓝色
                    frame[:, :, 1] = (color_value + 85) % 256  # 绿色
                    frame[:, :, 2] = (color_value + 170) % 256  # 红色
                    
                    # 添加文字标识
                    cv2.putText(frame, f"Codec: {codec}", (50, 100), 
                              cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
                    cv2.putText(frame, f"Frame: {i+1}", (50, 200), 
                              cv2.FONT_HERSHEY_SIMPLEX, 1.5, (255, 255, 255), 2)
                    
                    writer.write(frame)
                
                writer.release()
                
                # 检查文件
                if os.path.exists(test_file):
                    file_size = os.path.getsize(test_file)
                    print(f"✓ 视频文件创建成功: {file_size:,} bytes")
                    
                    # 测试视频可读性
                    cap = cv2.VideoCapture(test_file)
                    if cap.isOpened():
                        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                        actual_fps = cap.get(cv2.CAP_PROP_FPS)
                        print(f"✓ 视频可读取: {frame_count} 帧, {actual_fps:.1f} FPS")
                        cap.release()
                        
                        # 浏览器兼容性评估
                        browser_compatibility = assess_browser_compatibility(codec, ext)
                        results.append({
                            'codec': codec,
                            'extension': ext,
                            'description': description,
                            'file_size': file_size,
                            'browser_compatibility': browser_compatibility,
                            'status': 'success'
                        })
                    else:
                        print("✗ 视频无法读取")
                        results.append({
                            'codec': codec,
                            'extension': ext,
                            'description': description,
                            'status': 'failed_read'
                        })
                else:
                    print("✗ 视频文件未创建")
                    results.append({
                        'codec': codec,
                        'extension': ext,
                        'description': description,
                        'status': 'failed_create'
                    })
            else:
                print("✗ 编码器不可用")
                results.append({
                    'codec': codec,
                    'extension': ext,
                    'description': description,
                    'status': 'unavailable'
                })
                
        except Exception as e:
            print(f"✗ 测试失败: {e}")
            results.append({
                'codec': codec,
                'extension': ext,
                'description': description,
                'status': 'error',
                'error': str(e)
            })
        
        # 清理测试文件
        if os.path.exists(test_file):
            try:
                os.remove(test_file)
            except:
                pass
    
    # 输出总结报告
    print_summary_report(results)
    
    return results

def assess_browser_compatibility(codec, ext):
    """评估浏览器兼容性"""
    compatibility_map = {
        'H264': {
            'Chrome': 'Excellent',
            'Firefox': 'Excellent', 
            'Safari': 'Excellent',
            'Edge': 'Excellent',
            'IE': 'Good'
        },
        'mp4v': {
            'Chrome': 'Poor',
            'Firefox': 'Fair',
            'Safari': 'Fair',
            'Edge': 'Poor',
            'IE': 'Poor'
        },
        'VP80': {
            'Chrome': 'Excellent',
            'Firefox': 'Excellent',
            'Safari': 'Poor',
            'Edge': 'Good',
            'IE': 'Poor'
        },
        'VP90': {
            'Chrome': 'Excellent',
            'Firefox': 'Excellent',
            'Safari': 'Poor',
            'Edge': 'Good',
            'IE': 'Poor'
        },
        'XVID': {
            'Chrome': 'Poor',
            'Firefox': 'Poor',
            'Safari': 'Poor',
            'Edge': 'Poor',
            'IE': 'Poor'
        },
        'MJPG': {
            'Chrome': 'Fair',
            'Firefox': 'Fair',
            'Safari': 'Fair',
            'Edge': 'Fair',
            'IE': 'Fair'
        }
    }
    
    return compatibility_map.get(codec, {
        'Chrome': 'Unknown',
        'Firefox': 'Unknown',
        'Safari': 'Unknown',
        'Edge': 'Unknown',
        'IE': 'Unknown'
    })

def print_summary_report(results):
    """打印总结报告"""
    print("\n" + "=" * 60)
    print("浏览器兼容性总结报告")
    print("=" * 60)
    
    # 按兼容性排序
    success_results = [r for r in results if r['status'] == 'success']
    success_results.sort(key=lambda x: x.get('file_size', 0))
    
    print("\n推荐编码器（按浏览器兼容性排序）：")
    print("-" * 40)
    
    for i, result in enumerate(success_results, 1):
        codec = result['codec']
        ext = result['extension']
        size = result['file_size']
        compatibility = result['browser_compatibility']
        
        print(f"{i}. {codec}{ext} ({size:,} bytes)")
        print(f"   兼容性: Chrome({compatibility['Chrome']}) "
              f"Firefox({compatibility['Firefox']}) "
              f"Safari({compatibility['Safari']}) "
              f"Edge({compatibility['Edge']})")
        print()
    
    print("\n编码器建议：")
    print("-" * 20)
    print("1. H264 (.mp4) - 最佳选择，所有现代浏览器都支持")
    print("2. VP80/VP90 (.webm) - 开源选择，Chrome/Firefox支持良好")
    print("3. mp4v (.mp4) - 备选方案，兼容性有限")
    print("4. XVID/MJPG (.avi) - 不推荐用于Web播放")

def check_ffmpeg_availability():
    """检查FFmpeg是否可用"""
    print("\n检查FFmpeg可用性...")
    ffmpeg_path = shutil.which('ffmpeg')
    
    if ffmpeg_path:
        print(f"✓ FFmpeg已安装: {ffmpeg_path}")
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version_line = result.stdout.split('\n')[0]
                print(f"✓ FFmpeg版本: {version_line}")
                return True
        except Exception as e:
            print(f"✗ FFmpeg检查失败: {e}")
    else:
        print("✗ FFmpeg未安装")
    
    return False

def main():
    """主函数"""
    print("视频编码器浏览器兼容性测试")
    print("=" * 50)
    
    # 检查FFmpeg
    ffmpeg_available = check_ffmpeg_availability()
    
    # 测试编码器
    results = test_codec_browser_compatibility()
    
    # 如果有FFmpeg，提供转换建议
    if ffmpeg_available:
        print("\nFFmpeg转换建议：")
        print("-" * 20)
        print("如果生成的视频在浏览器中无法播放，可以使用FFmpeg转换为H.264：")
        print("ffmpeg -i input.mp4 -c:v libx264 -preset medium -crf 23 output.mp4")
    
    return results

if __name__ == "__main__":
    main() 