#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试DataAccess修复
验证get_student_name_by_id方法是否正常工作
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from data_access import DataAccess

def test_data_access_methods():
    """测试DataAccess中的学生相关方法"""
    print("=== DataAccess方法测试 ===")
    
    try:
        # 创建DataAccess实例
        data_access = DataAccess()
        print("✅ DataAccess实例创建成功")
        
        # 测试方法是否存在
        methods_to_test = [
            'find_student_by_id',
            'get_student_name_by_id',
            'find_student_by_name',
            'create_student',
            'get_or_create_student_id',
            'get_all_students_from_table'
        ]
        
        print("\n检查方法是否存在:")
        for method_name in methods_to_test:
            if hasattr(data_access, method_name):
                print(f"✅ {method_name} - 存在")
            else:
                print(f"❌ {method_name} - 不存在")
        
        # 测试创建学生
        print("\n=== 测试创建学生 ===")
        try:
            student_id = data_access.create_student("测试学生", "TEST001")
            print(f"✅ 创建学生成功，学号: {student_id}")
        except Exception as e:
            print(f"⚠️  创建学生失败（可能已存在）: {e}")
        
        # 测试查找学生
        print("\n=== 测试查找学生 ===")
        try:
            student = data_access.find_student_by_id("TEST001")
            if student:
                print(f"✅ 查找学生成功: {student}")
            else:
                print("❌ 未找到学生")
        except Exception as e:
            print(f"❌ 查找学生失败: {e}")
        
        # 测试获取学生姓名
        print("\n=== 测试获取学生姓名 ===")
        try:
            student_name = data_access.get_student_name_by_id("TEST001")
            if student_name:
                print(f"✅ 获取学生姓名成功: {student_name}")
            else:
                print("❌ 未找到学生姓名")
        except Exception as e:
            print(f"❌ 获取学生姓名失败: {e}")
        
        # 测试获取所有学生
        print("\n=== 测试获取所有学生 ===")
        try:
            students = data_access.get_all_students_from_table()
            print(f"✅ 获取所有学生成功，共 {len(students)} 个学生")
            for student in students[:3]:  # 只显示前3个
                print(f"  - {student['student_id']}: {student['student_name']}")
        except Exception as e:
            print(f"❌ 获取所有学生失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ DataAccess测试失败: {e}")
        return False

def test_api_simulation():
    """模拟API调用测试"""
    print("\n=== API调用模拟测试 ===")
    
    try:
        data_access = DataAccess()
        
        # 模拟 /api/students/<student_id> 的逻辑
        student_id = "TEST001"
        print(f"模拟查询学号: {student_id}")
        
        # 首先在students表中查找
        student = data_access.find_student_by_id(student_id)
        if student:
            result = {
                "status": "success",
                "student_id": student['student_id'],
                "student_name": student['student_name']
            }
            print(f"✅ 在students表中找到: {result}")
            return True
        
        # 如果没找到，尝试通过get_student_name_by_id方法查找
        student_name = data_access.get_student_name_by_id(student_id)
        if student_name:
            result = {
                "status": "success",
                "student_id": student_id,
                "student_name": student_name
            }
            print(f"✅ 通过兼容性查找找到: {result}")
            return True
        else:
            print(f"❌ 未找到学号 {student_id} 对应的学生")
            return False
            
    except Exception as e:
        print(f"❌ API模拟测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("DataAccess修复验证测试")
    print("=" * 50)
    
    # 测试1: DataAccess方法
    success1 = test_data_access_methods()
    
    # 测试2: API调用模拟
    success2 = test_api_simulation()
    
    print("\n" + "=" * 50)
    print("=== 测试总结 ===")
    
    if success1 and success2:
        print("🎉 所有测试通过！")
        print("✅ DataAccess方法正常工作")
        print("✅ get_student_name_by_id方法已修复")
        print("✅ API调用逻辑正确")
    else:
        print("⚠️  部分测试失败")
        if not success1:
            print("❌ DataAccess方法测试失败")
        if not success2:
            print("❌ API调用模拟测试失败")
    
    print("\n现在可以正常使用学号登录功能了！")

if __name__ == "__main__":
    main()
