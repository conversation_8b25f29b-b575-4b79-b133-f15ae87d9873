#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试考试文件API
验证截图和违规记录API的exam_id路径结构
"""

import requests
import json
import sys
import os

def test_screenshot_api(server_url, exam_id, filename):
    """测试截图API"""
    print(f"\n=== 测试截图API ===")
    print(f"考试ID: {exam_id}")
    print(f"文件名: {filename}")
    
    try:
        # 测试新的截图URL格式
        screenshot_url = f"{server_url}/screenshots/{exam_id}/{filename}"
        response = requests.get(screenshot_url, timeout=10)
        
        print(f"截图URL: {screenshot_url}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 截图API调用成功")
            print(f"Content-Type: {response.headers.get('Content-Type', 'unknown')}")
            print(f"Content-Length: {response.headers.get('Content-Length', 'unknown')}")
            return True
        elif response.status_code == 404:
            print("⚠️  截图文件不存在")
            return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            try:
                error_info = response.json()
                print(f"错误信息: {error_info.get('message', 'unknown')}")
            except:
                print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def test_violations_api(server_url, exam_id):
    """测试违规记录API"""
    print(f"\n=== 测试违规记录API ===")
    print(f"考试ID: {exam_id}")
    
    try:
        # 测试违规记录API
        violations_url = f"{server_url}/api/exams/{exam_id}/violations"
        response = requests.get(violations_url, timeout=10)
        
        print(f"违规API URL: {violations_url}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            violations = response.json()
            print("✅ 违规记录API调用成功")
            print(f"违规记录数量: {len(violations)}")
            
            # 检查截图URL格式
            for i, violation in enumerate(violations[:3]):  # 只检查前3个
                screenshot_url = violation.get('screenshot_url')
                if screenshot_url:
                    print(f"违规记录 {i+1}:")
                    print(f"  学生: {violation.get('username', 'unknown')}")
                    print(f"  时间: {violation.get('timestamp', 'unknown')}")
                    print(f"  截图URL: {screenshot_url}")
                    
                    # 验证URL格式
                    expected_prefix = f"/screenshots/{exam_id}/"
                    if screenshot_url.startswith(expected_prefix):
                        print(f"  ✅ URL格式正确")
                    else:
                        print(f"  ❌ URL格式错误，期望前缀: {expected_prefix}")
                else:
                    print(f"违规记录 {i+1}: 无截图")
            
            return True, violations
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            try:
                error_info = response.json()
                print(f"错误信息: {error_info.get('message', 'unknown')}")
            except:
                print(f"响应内容: {response.text}")
            return False, []
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False, []

def test_violations_pagination(server_url, exam_id):
    """测试违规记录分页"""
    print(f"\n=== 测试违规记录分页 ===")
    
    try:
        # 测试分页参数
        violations_url = f"{server_url}/api/exams/{exam_id}/violations"
        params = {
            'page': 1,
            'per_page': 5
        }
        
        response = requests.get(violations_url, params=params, timeout=10)
        
        print(f"分页URL: {violations_url}?page=1&per_page=5")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            violations = response.json()
            print("✅ 分页API调用成功")
            print(f"返回记录数: {len(violations)}")
            print(f"期望最大记录数: 5")
            
            if len(violations) <= 5:
                print("✅ 分页限制正常")
            else:
                print("❌ 分页限制异常")
            
            return True
        else:
            print(f"❌ 分页测试失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 分页测试失败: {e}")
        return False

def test_exam_existence(server_url, exam_id):
    """测试考试是否存在"""
    print(f"\n=== 测试考试存在性 ===")
    print(f"考试ID: {exam_id}")
    
    try:
        # 通过获取考试信息来验证考试是否存在
        exams_url = f"{server_url}/api/exams"
        response = requests.get(exams_url, timeout=10)
        
        if response.status_code == 200:
            exams = response.json()
            exam_exists = any(exam['id'] == exam_id for exam in exams)
            
            if exam_exists:
                print("✅ 考试存在")
                return True
            else:
                print("⚠️  考试不存在")
                print("可用的考试ID:")
                for exam in exams[:5]:  # 显示前5个考试
                    print(f"  - ID: {exam['id']}, 名称: {exam.get('name', 'unknown')}")
                return False
        else:
            print(f"❌ 无法获取考试列表: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 检查考试存在性失败: {e}")
        return False

def main():
    """主测试函数"""
    print("考试文件API测试")
    print("=" * 50)
    
    # 配置
    server_url = "http://127.0.0.1:5000"
    exam_id = 1  # 默认测试考试ID
    test_filename = "screenshot_test_1_2024-01-01_10-00-00.png"  # 测试文件名
    
    # 允许用户自定义配置
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    if len(sys.argv) > 2:
        exam_id = int(sys.argv[2])
    if len(sys.argv) > 3:
        test_filename = sys.argv[3]
    
    print(f"服务器地址: {server_url}")
    print(f"测试考试ID: {exam_id}")
    print(f"测试文件名: {test_filename}")
    print("-" * 50)
    
    # 1. 测试考试是否存在
    print("\n" + "="*50)
    print("1. 检查考试存在性")
    exam_exists = test_exam_existence(server_url, exam_id)
    
    if not exam_exists:
        print("⚠️  考试不存在，某些测试可能失败")
    
    # 2. 测试违规记录API
    print("\n" + "="*50)
    print("2. 测试违规记录API")
    violations_success, violations = test_violations_api(server_url, exam_id)
    
    # 3. 测试违规记录分页
    print("\n" + "="*50)
    print("3. 测试违规记录分页")
    pagination_success = test_violations_pagination(server_url, exam_id)
    
    # 4. 测试截图API
    print("\n" + "="*50)
    print("4. 测试截图API")
    
    # 如果有违规记录，使用真实的截图文件名
    if violations_success and violations:
        for violation in violations[:2]:  # 测试前2个违规记录的截图
            screenshot_url = violation.get('screenshot_url')
            if screenshot_url:
                # 从URL中提取文件名
                filename = screenshot_url.split('/')[-1]
                test_screenshot_api(server_url, exam_id, filename)
    else:
        # 使用默认测试文件名
        test_screenshot_api(server_url, exam_id, test_filename)
    
    # 5. 总结
    print("\n" + "="*50)
    print("=== 测试总结 ===")
    
    success_count = 0
    total_tests = 3
    
    if violations_success:
        success_count += 1
        print("✅ 违规记录API正常")
    else:
        print("❌ 违规记录API异常")
    
    if pagination_success:
        success_count += 1
        print("✅ 违规记录分页正常")
    else:
        print("❌ 违规记录分页异常")
    
    # 截图API测试结果基于是否有真实文件
    print("ℹ️  截图API测试需要真实文件存在")
    
    print(f"\n测试通过率: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 主要API功能正常！")
        print("\nAPI路径结构:")
        print(f"- 违规记录: /api/exams/{{exam_id}}/violations")
        print(f"- 截图文件: /screenshots/{{exam_id}}/{{filename}}")
        print("\n✅ 新的exam_id路径结构已正确实现")
    else:
        print("⚠️  部分API功能异常，请检查服务器配置")

if __name__ == "__main__":
    main()
