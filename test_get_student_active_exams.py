#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试get_student_active_exams函数
验证根据学号返回学生可以参加的正在进行的考试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from data_access import DataAccess

def test_get_student_active_exams():
    """测试get_student_active_exams函数"""
    print("=== 测试get_student_active_exams函数 ===")
    
    try:
        # 创建DataAccess实例
        data_access = DataAccess()
        print("✅ DataAccess实例创建成功")
        
        # 测试学号
        test_student_ids = [
            "2023001",
            "TEST001", 
            "不存在的学号"
        ]
        
        for student_id in test_student_ids:
            print(f"\n--- 测试学号: {student_id} ---")
            
            try:
                # 调用函数
                active_exams = data_access.get_student_active_exams(student_id)
                
                print(f"✅ 函数调用成功")
                print(f"找到 {len(active_exams)} 个活跃考试")
                
                # 显示结果
                if active_exams:
                    for i, exam in enumerate(active_exams, 1):
                        print(f"\n  考试 {i}:")
                        print(f"    ID: {exam['id']}")
                        print(f"    名称: {exam['name']}")
                        print(f"    状态: {exam['status']}")
                        print(f"    学生状态: {exam['student_status']}")
                        print(f"    开始时间: {exam['start_time']}")
                        print(f"    结束时间: {exam['end_time']}")
                        print(f"    最后活跃: {exam.get('last_active', '无')}")
                        
                        # 验证考试状态
                        if exam['status'] != 'active':
                            print(f"    ⚠️  警告: 考试状态不是active: {exam['status']}")
                else:
                    print("  没有找到活跃考试")
                
            except Exception as e:
                print(f"❌ 函数调用失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_function_exists():
    """测试函数是否存在"""
    print("=== 检查函数是否存在 ===")
    
    try:
        data_access = DataAccess()
        
        # 检查函数是否存在
        if hasattr(data_access, 'get_student_active_exams'):
            print("✅ get_student_active_exams 函数存在")
            
            # 检查是否可调用
            if callable(getattr(data_access, 'get_student_active_exams')):
                print("✅ get_student_active_exams 函数可调用")
                return True
            else:
                print("❌ get_student_active_exams 不可调用")
                return False
        else:
            print("❌ get_student_active_exams 函数不存在")
            return False
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def show_function_info():
    """显示函数信息"""
    print("=== 函数信息 ===")
    
    try:
        data_access = DataAccess()
        func = getattr(data_access, 'get_student_active_exams')
        
        print(f"函数名: {func.__name__}")
        print(f"文档字符串: {func.__doc__}")
        
        # 尝试获取函数签名
        import inspect
        sig = inspect.signature(func)
        print(f"函数签名: {sig}")
        
    except Exception as e:
        print(f"获取函数信息失败: {e}")

def main():
    """主测试函数"""
    print("get_student_active_exams 函数测试")
    print("=" * 50)
    
    # 1. 检查函数是否存在
    if not test_function_exists():
        print("❌ 函数不存在，测试终止")
        return
    
    # 2. 显示函数信息
    show_function_info()
    
    # 3. 测试函数功能
    print("\n" + "=" * 50)
    success = test_get_student_active_exams()
    
    # 4. 总结
    print("\n" + "=" * 50)
    print("=== 测试总结 ===")
    
    if success:
        print("🎉 测试通过！")
        print("✅ get_student_active_exams 函数已正确实现")
        print("✅ 可以根据学号返回学生的活跃考试")
        print("✅ 时间字段格式化正常")
        print("✅ 错误处理正常")
        
        print("\n函数功能:")
        print("- 根据学号查询学生参与的活跃考试")
        print("- 只返回状态为'active'的考试")
        print("- 包含学生在考试中的状态信息")
        print("- 自动格式化时间字段")
        print("- 按开始时间倒序排列")
    else:
        print("❌ 测试失败，请检查函数实现")

if __name__ == "__main__":
    main()
