-- 测试获取学生最新IP地址的SQL查询

-- 查询考试学生及其最新IP地址
SELECT es.*, 
       (SELECT slh.ip 
        FROM student_login_history slh 
        WHERE slh.student_exam_id = es.id 
          AND slh.ip IS NOT NULL 
        ORDER BY slh.timestamp DESC 
        LIMIT 1) as ip
FROM exam_students es 
WHERE es.exam_id = 4;

-- 验证查询：查看某个学生的登录历史
SELECT slh.*, es.student_name
FROM student_login_history slh
JOIN exam_students es ON slh.student_exam_id = es.id
WHERE es.exam_id = 4
ORDER BY slh.timestamp DESC;
