#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试登录修复是否有效
验证"student_id can't be null"错误是否已解决
"""

import requests
import json
import sys
from datetime import datetime

def test_login_api(server_url, username):
    """测试登录API"""
    print(f"测试登录API: {server_url}")
    print(f"用户名: {username}")
    
    try:
        # 测试登录请求
        login_data = {"username": username}
        response = requests.post(
            f"{server_url}/api/login",
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            status = result.get("status")
            
            if status == "success":
                print("✅ 登录成功！")
                print(f"考试ID: {result.get('exam_id')}")
                print(f"学生ID: {result.get('student_id')}")
                print(f"考试名称: {result.get('exam_name')}")
                return True, result
                
            elif status == "choice_required":
                print("ℹ️  需要选择考试")
                exams = result.get("exams", [])
                print(f"可用考试数量: {len(exams)}")
                for i, exam in enumerate(exams):
                    print(f"  {i+1}. {exam['name']} (ID: {exam['id']})")
                return "choice", result
                
            else:
                print(f"❌ 登录失败: {result.get('message')}")
                return False, result
        else:
            print(f"❌ 服务器错误: {response.status_code}")
            return False, {}
            
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False, {}

def test_exam_selection(server_url, username, exam_id):
    """测试考试选择API"""
    print(f"\n测试考试选择API")
    print(f"用户名: {username}, 考试ID: {exam_id}")
    
    try:
        selection_data = {
            "username": username,
            "exam_id": exam_id
        }
        response = requests.post(
            f"{server_url}/api/login/select_exam",
            json=selection_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == "success":
                print("✅ 考试选择登录成功！")
                print(f"考试ID: {result.get('exam_id')}")
                print(f"学生ID: {result.get('student_id')}")
                print(f"考试名称: {result.get('exam_name')}")
                return True, result
            else:
                print(f"❌ 考试选择失败: {result.get('message')}")
                return False, result
        else:
            print(f"❌ 服务器错误: {response.status_code}")
            return False, {}
            
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False, {}

def test_heartbeat(server_url, student_id, exam_id):
    """测试心跳API"""
    print(f"\n测试心跳API")
    print(f"学生ID: {student_id}, 考试ID: {exam_id}")
    
    try:
        heartbeat_data = {
            "student_id": student_id,
            "exam_id": exam_id
        }
        response = requests.post(
            f"{server_url}/api/heartbeat",
            json=heartbeat_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == "success":
                print("✅ 心跳发送成功！")
                return True
            else:
                print(f"❌ 心跳失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 服务器错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 登录修复测试 ===")
    
    # 配置
    server_url = "http://127.0.0.1:5000"  # 默认本地服务器
    username = "测试用户"  # 测试用户名
    
    # 允许用户自定义配置
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    if len(sys.argv) > 2:
        username = sys.argv[2]
    
    print(f"服务器地址: {server_url}")
    print(f"测试用户名: {username}")
    print("-" * 50)
    
    # 测试1: 基本登录
    print("\n1. 测试基本登录")
    success, login_result = test_login_api(server_url, username)
    
    if success is True:
        # 直接登录成功，测试心跳
        student_id = login_result.get('student_id')
        exam_id = login_result.get('exam_id')
        test_heartbeat(server_url, student_id, exam_id)
        
    elif success == "choice":
        # 需要选择考试
        print("\n2. 测试考试选择")
        exams = login_result.get("exams", [])
        if exams:
            # 选择第一个考试进行测试
            first_exam = exams[0]
            exam_id = first_exam['id']
            
            success2, selection_result = test_exam_selection(server_url, username, exam_id)
            if success2:
                # 考试选择成功，测试心跳
                student_id = selection_result.get('student_id')
                exam_id = selection_result.get('exam_id')
                test_heartbeat(server_url, student_id, exam_id)
    
    print("\n=== 测试完成 ===")
    
    # 总结
    if success:
        print("🎉 登录功能正常工作！")
        print("✅ student_id can't be null 错误已修复")
    else:
        print("⚠️  登录功能仍有问题，请检查服务器状态和数据库配置")

if __name__ == "__main__":
    main()
