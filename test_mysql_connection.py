#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试MySQL连接池是否正常工作
用于验证"Commands out of sync"错误是否已解决
"""

import sys
import os
from data_access import DataAccess

def test_basic_connection():
    """测试基本连接功能"""
    print("测试基本MySQL连接...")
    try:
        data_access = DataAccess()
        print("✅ DataAccess实例创建成功")
        
        # 测试获取连接
        conn = data_access.get_connection()
        print("✅ 获取数据库连接成功")
        conn.close()
        print("✅ 连接关闭成功")
        
        return True
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def test_multiple_operations():
    """测试多个数据库操作"""
    print("\n测试多个数据库操作...")
    try:
        data_access = DataAccess()
        
        # 测试获取所有考试
        exams = data_access.get_all_exams()
        print(f"✅ 获取考试列表成功，共 {len(exams)} 个考试")
        
        # 测试检查考试存在性
        if exams:
            exam_id = exams[0]['id']
            exists = data_access.exists(f'exam:{exam_id}')
            print(f"✅ 检查考试存在性成功: {exists}")
        
        # 测试获取所有学生
        students = data_access.get_all_students()
        print(f"✅ 获取学生列表成功，共 {len(students)} 个学生")
        
        return True
    except Exception as e:
        print(f"❌ 多操作测试失败: {e}")
        return False

def test_concurrent_operations():
    """测试并发操作"""
    print("\n测试并发操作...")
    import threading
    import time
    
    results = []
    
    def worker(worker_id):
        try:
            data_access = DataAccess()
            for i in range(5):
                exams = data_access.get_all_exams()
                time.sleep(0.1)  # 模拟处理时间
            results.append(f"Worker {worker_id}: 成功")
        except Exception as e:
            results.append(f"Worker {worker_id}: 失败 - {e}")
    
    # 创建多个线程
    threads = []
    for i in range(3):
        thread = threading.Thread(target=worker, args=(i,))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    # 检查结果
    success_count = sum(1 for result in results if "成功" in result)
    print(f"并发测试结果: {success_count}/{len(results)} 成功")
    
    for result in results:
        if "成功" in result:
            print(f"✅ {result}")
        else:
            print(f"❌ {result}")
    
    return success_count == len(results)

def main():
    """主测试函数"""
    print("=== MySQL连接池测试 ===")
    
    tests = [
        ("基本连接测试", test_basic_connection),
        ("多操作测试", test_multiple_operations),
        ("并发操作测试", test_concurrent_operations)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print(f"\n=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！MySQL连接池工作正常。")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和数据库连接。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
