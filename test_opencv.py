import cv2
import numpy as np
import os
import sys

def check_opencv_info():
    """检查OpenCV基本信息"""
    print("="*50)
    print("OpenCV 基本信息")
    print("="*50)
    print(f"OpenCV版本: {cv2.__version__}")
    print(f"Python版本: {sys.version}")
    
    # 检查OpenCV编译信息
    build_info = cv2.getBuildInformation()
    print("\n重要编译信息:")
    
    # 提取关键信息
    lines = build_info.split('\n')
    important_keys = [
        'Video I/O:',
        'FFMPEG:',
        'GStreamer:',
        'libv4l/libv4l2:',
        'v4l/v4l2:',
        'OpenNI:',
        'OpenNI2:',
        'TIFF:',
        'JPEG:',
        'PNG:',
        'OpenEXR:',
        'GDAL:',
        'GDCM:'
    ]
    
    for line in lines:
        for key in important_keys:
            if key in line:
                print(f"  {line.strip()}")
    
    print("\n" + "="*50)

def check_video_codecs():
    """检查视频编解码器支持"""
    print("视频编解码器测试")
    print("="*50)
    
    # 测试常见编解码器
    codecs_to_test = [
        ('MP4V', 'mp4v', 'MPEG-4 Part 2'),
        ('H264', 'H264', 'H.264/AVC'),
        ('X264', 'X264', 'x264编码器'),
        ('MJPG', 'MJPG', 'Motion JPEG'),
        ('XVID', 'XVID', 'Xvid MPEG-4'),
        ('VP80', 'VP80', 'VP8 (WebM)'),
        ('VP90', 'VP90', 'VP9 (WebM)'),
        ('AVC1', 'AVC1', 'Advanced Video Coding'),
        ('FMP4', 'FMP4', 'FFmpeg MPEG-4'),
        ('DIV3', 'DIV3', 'DivX 3'),
        ('DIVX', 'DIVX', 'DivX'),
        ('U263', 'U263', 'UB Video H.263'),
        ('I263', 'I263', 'Intel H.263'),
        ('FLVI', 'FLVI', 'Flash Video'),
        ('THEO', 'THEO', 'Theora'),
        ('WMV1', 'WMV1', 'Windows Media Video 7'),
        ('WMV2', 'WMV2', 'Windows Media Video 8'),
        ('WMV3', 'WMV3', 'Windows Media Video 9')
    ]
    
    supported_codecs = []
    height, width = 100, 100
    test_filename = 'codec_test.avi'
    
    print("正在测试编解码器支持...")
    print(f"{'编码器':<8} {'状态':<6} {'描述'}")
    print("-" * 40)
    
    for name, codec, description in codecs_to_test:
        try:
            fourcc = cv2.VideoWriter_fourcc(*codec)
            out = cv2.VideoWriter(test_filename, fourcc, 30, (width, height))
            
            if out.isOpened():
                # 尝试写入一帧测试
                test_frame = np.zeros((height, width, 3), dtype=np.uint8)
                out.write(test_frame)
                out.release()
                
                # 检查文件是否成功创建
                if os.path.exists(test_filename) and os.path.getsize(test_filename) > 0:
                    supported_codecs.append((name, codec, description))
                    print(f"{name:<8} {'✓':<6} {description}")
                    status = "✓"
                else:
                    print(f"{name:<8} {'✗':<6} {description}")
                    status = "✗"
                
                # 清理测试文件
                if os.path.exists(test_filename):
                    os.remove(test_filename)
            else:
                print(f"{name:<8} {'✗':<6} {description}")
                out.release()
                
        except Exception as e:
            print(f"{name:<8} {'✗':<6} {description} (错误: {str(e)[:20]})")
    
    return supported_codecs

def check_webm_support():
    """专门检查WebM(VP8/VP9)支持"""
    print("\n" + "="*50)
    print("WebM 支持详细检查")
    print("="*50)
    
    # 检查VP8支持
    print("VP8 编码器测试:")
    vp8_supported = test_specific_codec('VP80', 'test_vp8.webm')
    
    print("\nVP9 编码器测试:")
    vp9_supported = test_specific_codec('VP90', 'test_vp9.webm')
    
    # 检查WebM文件扩展名支持
    print("\n扩展名测试:")
    webm_ext_supported = test_specific_codec('VP80', 'test.webm')
    
    # 总结WebM支持情况
    print(f"\n{'='*30}")
    print("WebM 支持总结:")
    print(f"VP8 编码器: {'✓ 支持' if vp8_supported else '✗ 不支持'}")
    print(f"VP9 编码器: {'✓ 支持' if vp9_supported else '✗ 不支持'}")
    print(f"WebM 格式: {'✓ 支持' if webm_ext_supported else '✗ 不支持'}")
    
    if not (vp8_supported or vp9_supported):
        print("\n❌ 当前OpenCV不支持WebM格式")
        print("原因可能是:")
        print("1. OpenCV编译时未启用libvpx支持")
        print("2. 系统缺少libvpx库")
        print("3. 使用的是精简版OpenCV")
        print("\n解决方案:")
        print("1. 重新安装完整版OpenCV: pip install opencv-contrib-python")
        print("2. 或使用MP4格式代替WebM")
    else:
        print("\n✅ WebM格式支持正常")

def test_specific_codec(codec, filename):
    """测试特定编解码器"""
    try:
        fourcc = cv2.VideoWriter_fourcc(*codec)
        out = cv2.VideoWriter(filename, fourcc, 30, (100, 100))
        
        if out.isOpened():
            # 写入测试帧
            test_frame = np.zeros((100, 100, 3), dtype=np.uint8)
            test_frame[:, :] = [0, 255, 0]  # 绿色帧
            
            for i in range(10):  # 写入10帧
                out.write(test_frame)
            
            out.release()
            
            # 检查文件
            if os.path.exists(filename) and os.path.getsize(filename) > 0:
                print(f"  ✓ {codec} 编码器工作正常")
                print(f"  ✓ 生成文件: {filename} ({os.path.getsize(filename)} bytes)")
                
                # 尝试读取验证
                cap = cv2.VideoCapture(filename)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret:
                        print(f"  ✓ 文件可以正常读取")
                    else:
                        print(f"  ⚠ 文件创建成功但无法读取")
                    cap.release()
                else:
                    print(f"  ⚠ 文件创建成功但无法打开")
                
                # 清理
                os.remove(filename)
                return True
            else:
                print(f"  ✗ {codec} 编码器无法创建有效文件")
                out.release()
                return False
        else:
            print(f"  ✗ {codec} 编码器无法打开")
            out.release()
            return False
            
    except Exception as e:
        print(f"  ✗ {codec} 编码器测试失败: {str(e)}")
        return False

def get_recommended_settings():
    """获取推荐的编码设置"""
    print("\n" + "="*50)
    print("推荐的编码设置")
    print("="*50)
    
    print("根据测试结果，推荐使用以下设置:")
    print()
    
    # 检查支持的编码器
    supported = check_video_codecs()
    
    # 按优先级排序
    priority_codecs = [
        ('H264', 'H264', 'H.264 - 最佳浏览器兼容性'),
        ('MP4V', 'mp4v', 'MPEG-4 - 广泛支持'),
        ('X264', 'X264', 'x264 - 高质量'),
        ('MJPG', 'MJPG', 'Motion JPEG - 备用选择'),
        ('VP80', 'VP80', 'VP8 - WebM格式'),
        ('VP90', 'VP90', 'VP9 - 现代WebM格式')
    ]
    
    print("推荐优先级:")
    for i, (name, codec, desc) in enumerate(priority_codecs, 1):
        # 检查是否支持
        is_supported = any(s[0] == name for s in supported)
        status = "✓ 支持" if is_supported else "✗ 不支持"
        print(f"{i}. {name:<6} - {desc:<30} [{status}]")
    
    # 给出具体建议
    print("\n具体建议:")
    h264_supported = any(s[0] == 'H264' for s in supported)
    mp4v_supported = any(s[0] == 'MP4V' for s in supported)
    vp8_supported = any(s[0] == 'VP80' for s in supported)
    
    if h264_supported:
        print("✅ 使用 H264 编码器，MP4格式 (最佳选择)")
        print("   代码: fourcc = cv2.VideoWriter_fourcc(*'H264')")
    elif mp4v_supported:
        print("✅ 使用 MP4V 编码器，MP4格式 (推荐)")
        print("   代码: fourcc = cv2.VideoWriter_fourcc(*'mp4v')")
    else:
        print("⚠️ 建议重新安装OpenCV或使用MJPG编码器")
    
    if vp8_supported:
        print("✅ 如需WebM格式，可使用VP8编码器")
        print("   代码: fourcc = cv2.VideoWriter_fourcc(*'VP80')")

def main():
    """主函数"""
    print("OpenCV 视频编解码器支持检查工具")
    print("="*60)
    
    # 基本信息检查
    check_opencv_info()
    
    # 编解码器支持检查
    supported_codecs = check_video_codecs()
    
    # WebM专项检查
    check_webm_support()
    
    # 推荐设置
    get_recommended_settings()
    
    print("\n" + "="*60)
    print("检查完成！")
    print("="*60)

if __name__ == "__main__":
    main()