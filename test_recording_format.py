#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试录屏格式一致性
验证客户端和服务器端的录屏文件命名格式是否一致
"""

import os
import sys
import time
from datetime import datetime

def test_client_recording_format():
    """测试客户端录屏格式"""
    print("=== 测试客户端录屏格式 ===")
    
    # 模拟客户端录屏文件名生成
    student_id = "test_student_001"
    exam_id = 123
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 客户端应该生成的文件名格式
    client_filename = f"recording_{student_id}_{exam_id}_{timestamp}.mp4"
    print(f"客户端文件名格式: {client_filename}")
    
    # 检查格式是否符合服务器端期望
    expected_prefix = f"recording_{student_id}_{exam_id}_"
    if client_filename.startswith(expected_prefix):
        print("✓ 客户端文件名格式正确")
    else:
        print("✗ 客户端文件名格式错误")
        return False
    
    return True

def test_server_recording_format():
    """测试服务器端录屏格式识别"""
    print("\n=== 测试服务器端录屏格式识别 ===")
    
    # 模拟服务器端文件识别逻辑
    test_files = [
        "recording_test_student_001_123_20241201_143022.mp4",
        "recording_test_student_002_123_20241201_143023.webm", 
        "recording_test_student_003_123_20241201_143024.avi",
        "old_format_screen_test_student_001_20241201_143025.mp4",  # 旧格式
        "merged_test_student_001_123_20241201_143026.mp4"  # 合并后的文件
    ]
    
    student_id = "test_student_001"
    exam_id = 123
    
    print("测试文件列表:")
    for filename in test_files:
        print(f"  {filename}")
    
    print(f"\n查找学生 {student_id} 在考试 {exam_id} 中的录屏片段:")
    
    # 服务器端识别逻辑
    recordings = []
    for filename in test_files:
        # 检查是否是录屏片段: recording_{student_id}_{exam_id}_*.{ext}
        if (filename.startswith(f"recording_{student_id}_{exam_id}_") and 
            filename.endswith(('.mp4', '.webm', '.avi'))):
            recordings.append(filename)
            print(f"  ✓ 识别为录屏片段: {filename}")
        elif filename.startswith(f"recording_{student_id}_{exam_id}_"):
            print(f"  ⚠ 格式匹配但扩展名不支持: {filename}")
        else:
            print(f"  ✗ 不匹配: {filename}")
    
    print(f"\n找到 {len(recordings)} 个录屏片段")
    return len(recordings) > 0

def test_merged_recording_format():
    """测试合并录屏格式"""
    print("\n=== 测试合并录屏格式 ===")
    
    # 模拟合并后的文件名
    student_name = "张三"
    exam_id = 123
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    merged_filename = f"{student_name}_{exam_id}_{timestamp}.mp4"
    print(f"合并后文件名格式: {merged_filename}")
    
    # 检查格式是否符合服务器端期望
    expected_prefix = f"{student_name}_{exam_id}_"
    if merged_filename.startswith(expected_prefix) and merged_filename.endswith('.mp4'):
        print("✓ 合并录屏文件名格式正确")
    else:
        print("✗ 合并录屏文件名格式错误")
        return False
    
    return True

def test_file_extension_handling():
    """测试文件扩展名处理"""
    print("\n=== 测试文件扩展名处理 ===")
    
    # 测试不同编码器对应的扩展名
    codec_extensions = {
        'VP80': '.webm',
        'VP90': '.webm', 
        'mp4v': '.mp4',
        'XVID': '.avi',
        'MJPG': '.avi',
        'H264': '.mp4'
    }
    
    print("编码器与扩展名对应关系:")
    for codec, ext in codec_extensions.items():
        print(f"  {codec} -> {ext}")
    
    # 测试文件名重命名逻辑
    original_path = "recording_test_001_123_20241201_143022.mp4"
    selected_codec = "VP80"
    
    if selected_codec in codec_extensions:
        new_extension = codec_extensions[selected_codec]
        name_without_ext = os.path.splitext(original_path)[0]
        new_path = name_without_ext + new_extension
        
        print(f"\n文件名重命名示例:")
        print(f"  原始: {original_path}")
        print(f"  编码器: {selected_codec}")
        print(f"  新扩展名: {new_extension}")
        print(f"  重命名后: {new_path}")
        
        if new_path == "recording_test_001_123_20241201_143022.webm":
            print("✓ 文件名重命名逻辑正确")
            return True
        else:
            print("✗ 文件名重命名逻辑错误")
            return False
    
    return False

def main():
    """主测试函数"""
    print("录屏格式一致性测试")
    print("=" * 50)
    
    tests = [
        test_client_recording_format,
        test_server_recording_format,
        test_merged_recording_format,
        test_file_extension_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"测试失败: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！客户端和服务器端录屏格式一致")
        return True
    else:
        print("✗ 部分测试失败，需要检查格式一致性")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 