#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
录屏功能测试脚本
"""

import os
import sys
import time
import requests
import json
from datetime import datetime

def test_screen_recording():
    """测试录屏功能"""
    
    # 服务器配置
    server_url = "http://localhost:5000"
    
    # 测试数据
    test_student_id = "test_student_001"
    test_exam_id = "1"
    
    print("=== 录屏功能测试 ===")
    
    # 1. 测试单帧上传
    print("\n1. 测试单帧上传...")
    try:
        # 创建一个简单的测试图像（这里用文本模拟）
        import base64
        test_image_data = base64.b64encode(b"test_image_data").decode('utf-8')
        
        frame_data = {
            'student_id': test_student_id,
            'exam_id': test_exam_id,
            'timestamp': datetime.now().isoformat(),
            'image_data': test_image_data,
            'type': 'single_frame'
        }
        
        response = requests.post(
            f"{server_url}/api/screen_frame",
            json=frame_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 单帧上传成功: {result.get('message')}")
        else:
            print(f"✗ 单帧上传失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"✗ 单帧上传测试出错: {e}")
    
    # 2. 测试录屏文件上传
    print("\n2. 测试录屏文件上传...")
    try:
        # 创建一个测试视频文件
        test_video_path = "test_video.mp4"
        with open(test_video_path, 'wb') as f:
            f.write(b"test_video_data")
        
        with open(test_video_path, 'rb') as f:
            files = {'video': f}
            data = {
                'student_id': test_student_id,
                'exam_id': test_exam_id,
                'timestamp': datetime.now().isoformat(),
                'fps': 10,
                'quality': 80
            }
            
            response = requests.post(
                f"{server_url}/api/screen_recording",
                files=files,
                data=data,
                timeout=30
            )
        
        # 删除测试文件
        if os.path.exists(test_video_path):
            os.remove(test_video_path)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ 录屏上传成功: {result.get('message')}")
        else:
            print(f"✗ 录屏上传失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"✗ 录屏上传测试出错: {e}")
    
    # 3. 测试获取录屏列表
    print("\n3. 测试获取录屏列表...")
    try:
        response = requests.get(
            f"{server_url}/api/exams/{test_exam_id}/students/{test_student_id}/recordings",
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            recordings = result.get('recordings', [])
            print(f"✓ 获取录屏列表成功，共 {len(recordings)} 个录屏文件")
            for recording in recordings[:3]:  # 只显示前3个
                print(f"  - {recording.get('filename')} ({recording.get('file_size', 0)} bytes)")
        else:
            print(f"✗ 获取录屏列表失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"✗ 获取录屏列表测试出错: {e}")
    
    # 4. 测试获取帧列表
    print("\n4. 测试获取帧列表...")
    try:
        response = requests.get(
            f"{server_url}/api/exams/{test_exam_id}/students/{test_student_id}/frames",
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            frames = result.get('frames', [])
            print(f"✓ 获取帧列表成功，共 {len(frames)} 个帧文件")
            for frame in frames[:3]:  # 只显示前3个
                print(f"  - {frame.get('filename')}")
        else:
            print(f"✗ 获取帧列表失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"✗ 获取帧列表测试出错: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_screen_recording() 