#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试通过username查找student_id功能
验证学生ID管理系统是否正常工作
"""

import requests
import json
import sys

def test_create_student(server_url, student_name, student_id=None):
    """测试创建学生"""
    print(f"\n=== 测试创建学生 ===")
    print(f"学生姓名: {student_name}")
    print(f"学生ID: {student_id or '自动生成'}")
    
    try:
        data = {"student_name": student_name}
        if student_id:
            data["student_id"] = student_id
            
        response = requests.post(
            f"{server_url}/api/students",
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == "success":
                print(f"✅ 学生创建成功!")
                print(f"分配的学生ID: {result.get('student_id')}")
                return True, result.get('student_id')
            else:
                print(f"❌ 创建失败: {result.get('message')}")
                return False, None
        else:
            print(f"❌ 服务器错误: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False, None

def test_batch_create_students(server_url, students_data):
    """测试批量创建学生"""
    print(f"\n=== 测试批量创建学生 ===")
    print(f"学生数量: {len(students_data)}")
    
    try:
        data = {"students": students_data}
        response = requests.post(
            f"{server_url}/api/students/batch",
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == "success":
                print(f"✅ 批量创建成功!")
                created = result.get('created_students', [])
                errors = result.get('errors', [])
                print(f"成功创建: {len(created)} 个学生")
                if errors:
                    print(f"失败: {len(errors)} 个")
                    for error in errors:
                        print(f"  - {error}")
                return True, created
            else:
                print(f"❌ 批量创建失败: {result.get('message')}")
                return False, []
        else:
            print(f"❌ 服务器错误: {response.status_code}")
            return False, []
            
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False, []

def test_get_all_students(server_url):
    """测试获取所有学生"""
    print(f"\n=== 测试获取所有学生 ===")
    
    try:
        response = requests.get(
            f"{server_url}/api/students",
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == "success":
                students = result.get('students', [])
                print(f"✅ 获取成功! 共 {len(students)} 个学生")
                for student in students:
                    print(f"  - ID: {student['student_id']}, 姓名: {student['student_name']}")
                return True, students
            else:
                print(f"❌ 获取失败: {result.get('message')}")
                return False, []
        else:
            print(f"❌ 服务器错误: {response.status_code}")
            return False, []
            
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False, []

def test_login_with_username(server_url, username):
    """测试使用用户名登录（验证student_id查找）"""
    print(f"\n=== 测试用户名登录 ===")
    print(f"用户名: {username}")
    
    try:
        data = {"username": username}
        response = requests.post(
            f"{server_url}/api/login",
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            status = result.get("status")
            
            if status == "success":
                print(f"✅ 登录成功!")
                print(f"查找到的学生ID: {result.get('student_id')}")
                print(f"考试ID: {result.get('exam_id')}")
                return True, result
            elif status == "choice_required":
                print(f"ℹ️  需要选择考试")
                return "choice", result
            else:
                print(f"❌ 登录失败: {result.get('message')}")
                return False, result
        else:
            print(f"❌ 服务器错误: {response.status_code}")
            return False, {}
            
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False, {}

def main():
    """主测试函数"""
    print("=== 学生ID查找功能测试 ===")
    
    # 配置
    server_url = "http://127.0.0.1:5000"
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    
    print(f"服务器地址: {server_url}")
    print("-" * 50)
    
    # 测试数据
    test_students = [
        {"student_name": "张三", "student_id": "2023001"},
        {"student_name": "李四", "student_id": "2023002"},
        {"student_name": "王五"},  # 不指定ID，自动生成
        {"student_name": "赵六"},  # 不指定ID，自动生成
    ]
    
    # 1. 测试单个学生创建
    print("\n" + "="*50)
    print("1. 测试单个学生创建")
    test_create_student(server_url, "测试学生1", "TEST001")
    test_create_student(server_url, "测试学生2")  # 自动生成ID
    
    # 2. 测试批量学生创建
    print("\n" + "="*50)
    print("2. 测试批量学生创建")
    test_batch_create_students(server_url, test_students)
    
    # 3. 测试获取所有学生
    print("\n" + "="*50)
    print("3. 测试获取所有学生")
    success, students = test_get_all_students(server_url)
    
    # 4. 测试登录功能（验证student_id查找）
    print("\n" + "="*50)
    print("4. 测试登录功能")
    if success and students:
        # 使用已创建的学生进行登录测试
        for student in students[:3]:  # 测试前3个学生
            test_login_with_username(server_url, student['student_name'])
    else:
        # 使用默认用户名测试
        test_login_with_username(server_url, "张三")
        test_login_with_username(server_url, "新用户")  # 测试自动创建
    
    print("\n" + "="*50)
    print("=== 测试完成 ===")
    print("\n功能验证:")
    print("✅ 学生ID自动生成")
    print("✅ 通过姓名查找学生ID")
    print("✅ 批量学生管理")
    print("✅ 登录时自动关联学生ID")

if __name__ == "__main__":
    main()
