#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试学号登录功能
验证通过学号查询姓名并登录的流程
"""

import requests
import json
import sys

def test_get_student_by_id(server_url, student_id):
    """测试通过学号获取学生信息"""
    print(f"\n=== 测试通过学号获取学生信息 ===")
    print(f"学号: {student_id}")
    
    try:
        response = requests.get(
            f"{server_url}/api/students/{student_id}",
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == "success":
                print(f"✅ 查询成功!")
                print(f"学生姓名: {result.get('student_name')}")
                return True, result.get('student_name')
            else:
                print(f"❌ 查询失败: {result.get('message')}")
                return False, None
        elif response.status_code == 404:
            print(f"❌ 未找到该学号对应的学生")
            return False, None
        else:
            print(f"❌ 服务器错误: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False, None

def test_login_with_student_info(server_url, student_id, student_name):
    """测试使用学号和姓名登录"""
    print(f"\n=== 测试使用学号和姓名登录 ===")
    print(f"学号: {student_id}")
    print(f"姓名: {student_name}")
    
    try:
        data = {
            "student_id": student_id,
            "student_name": student_name
        }
        response = requests.post(
            f"{server_url}/api/login_with_student_info",
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            status = result.get("status")
            
            if status == "success":
                print(f"✅ 登录成功!")
                print(f"考试ID: {result.get('exam_id')}")
                print(f"考试名称: {result.get('exam_name')}")
                return True, result
            elif status == "choice_required":
                print(f"ℹ️  需要选择考试")
                exams = result.get("exams", [])
                print(f"可用考试数量: {len(exams)}")
                for i, exam in enumerate(exams):
                    print(f"  {i+1}. {exam['name']} (ID: {exam['id']})")
                return "choice", result
            else:
                print(f"❌ 登录失败: {result.get('message')}")
                return False, result
        else:
            print(f"❌ 服务器错误: {response.status_code}")
            return False, {}
            
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False, {}

def test_select_exam_login(server_url, student_name, exam_id):
    """测试选择考试登录"""
    print(f"\n=== 测试选择考试登录 ===")
    print(f"学生姓名: {student_name}, 考试ID: {exam_id}")
    
    try:
        data = {
            "username": student_name,
            "exam_id": exam_id
        }
        response = requests.post(
            f"{server_url}/api/login/select_exam",
            json=data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("status") == "success":
                print(f"✅ 考试选择登录成功!")
                print(f"考试ID: {result.get('exam_id')}")
                print(f"学生ID: {result.get('student_id')}")
                print(f"考试名称: {result.get('exam_name')}")
                return True, result
            else:
                print(f"❌ 考试选择失败: {result.get('message')}")
                return False, result
        else:
            print(f"❌ 服务器错误: {response.status_code}")
            return False, {}
            
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False, {}

def main():
    """主测试函数"""
    print("=== 学号登录功能测试 ===")
    
    # 配置
    server_url = "http://127.0.0.1:5000"
    student_id = "2023001"  # 默认测试学号
    
    # 允许用户自定义配置
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    if len(sys.argv) > 2:
        student_id = sys.argv[2]
    
    print(f"服务器地址: {server_url}")
    print(f"测试学号: {student_id}")
    print("-" * 50)
    
    # 1. 测试通过学号获取学生信息
    print("\n" + "="*50)
    print("1. 测试通过学号获取学生信息")
    success, student_name = test_get_student_by_id(server_url, student_id)
    
    if success and student_name:
        # 2. 测试使用学号和姓名登录
        print("\n" + "="*50)
        print("2. 测试使用学号和姓名登录")
        login_success, login_result = test_login_with_student_info(server_url, student_id, student_name)
        
        if login_success == "choice" and login_result.get("exams"):
            # 3. 测试选择考试登录
            print("\n" + "="*50)
            print("3. 测试选择考试登录")
            exams = login_result.get("exams", [])
            if exams:
                # 选择第一个考试进行测试
                first_exam = exams[0]
                exam_id = first_exam['id']
                test_select_exam_login(server_url, student_name, exam_id)
    else:
        # 如果学号不存在，测试创建学生
        print("\n" + "="*50)
        print("创建测试学生")
        create_url = f"{server_url}/api/students"
        try:
            data = {
                "student_id": student_id,
                "student_name": f"测试学生_{student_id}"
            }
            response = requests.post(
                create_url,
                json=data,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            if response.status_code == 200:
                print("✅ 创建测试学生成功，请重新运行测试")
            else:
                print("❌ 创建测试学生失败")
        except Exception as e:
            print(f"❌ 连接错误: {e}")
    
    print("\n" + "="*50)
    print("=== 测试完成 ===")
    
    # 总结
    if success:
        print("\n功能验证:")
        print("✅ 通过学号查询学生姓名")
        print("✅ 使用学号和姓名登录")
        print("✅ 考试选择功能")
        print("\n登录流程正常工作！")
    else:
        print("\n⚠️  测试未完成，请检查服务器状态和数据库配置")

if __name__ == "__main__":
    main()
