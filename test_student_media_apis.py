#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试学生媒体文件API
验证截图、违规记录和录屏视频的API功能
"""

import requests
import json
import sys

def test_student_screenshots_api(server_url, exam_id, student_id):
    """测试学生截图API"""
    print(f"\n=== 测试学生截图API ===")
    print(f"考试ID: {exam_id}, 学生ID: {student_id}")
    
    try:
        # 测试截图API
        screenshots_url = f"{server_url}/api/exams/{exam_id}/students/{student_id}/screenshots"
        response = requests.get(screenshots_url, timeout=10)
        
        print(f"截图API URL: {screenshots_url}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            screenshots = result.get('screenshots', [])
            
            print("✅ 截图API调用成功")
            print(f"截图数量: {len(screenshots)}")
            
            # 检查URL格式
            for i, screenshot_url in enumerate(screenshots[:3]):  # 只检查前3个
                print(f"截图 {i+1}: {screenshot_url}")
                
                # 验证URL格式
                expected_prefix = f"/screenshots/{exam_id}/"
                if screenshot_url.startswith(expected_prefix):
                    print(f"  ✅ URL格式正确")
                    
                    # 测试实际访问截图文件
                    full_url = f"{server_url}{screenshot_url}"
                    img_response = requests.get(full_url, timeout=5)
                    if img_response.status_code == 200:
                        print(f"  ✅ 截图文件可访问")
                    else:
                        print(f"  ⚠️  截图文件不可访问: {img_response.status_code}")
                else:
                    print(f"  ❌ URL格式错误，期望前缀: {expected_prefix}")
            
            return True, screenshots
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            try:
                error_info = response.json()
                print(f"错误信息: {error_info.get('message', 'unknown')}")
            except:
                print(f"响应内容: {response.text}")
            return False, []
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False, []

def test_student_recordings_api(server_url, exam_id, student_id):
    """测试学生录屏API"""
    print(f"\n=== 测试学生录屏API ===")
    print(f"考试ID: {exam_id}, 学生ID: {student_id}")
    
    try:
        # 测试录屏API
        recordings_url = f"{server_url}/api/exams/{exam_id}/students/{student_id}/recordings"
        response = requests.get(recordings_url, timeout=10)
        
        print(f"录屏API URL: {recordings_url}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            recordings = result.get('recordings', [])
            
            print("✅ 录屏API调用成功")
            print(f"录屏文件数量: {len(recordings)}")
            
            # 检查录屏文件信息
            for i, recording in enumerate(recordings[:3]):  # 只检查前3个
                print(f"\n录屏 {i+1}:")
                print(f"  文件名: {recording.get('filename', 'unknown')}")
                print(f"  类型: {recording.get('type', 'unknown')}")
                print(f"  大小: {recording.get('file_size', 0)} bytes")
                print(f"  创建时间: {recording.get('created_time', 'unknown')}")
                print(f"  下载URL: {recording.get('download_url', 'unknown')}")
                
                # 验证URL格式
                download_url = recording.get('download_url', '')
                expected_prefix = f"/recordings/{exam_id}/"
                if download_url.startswith(expected_prefix):
                    print(f"  ✅ URL格式正确")
                    
                    # 测试实际访问录屏文件（只检查头部，不下载完整文件）
                    full_url = f"{server_url}{download_url}"
                    try:
                        video_response = requests.head(full_url, timeout=5)
                        if video_response.status_code == 200:
                            print(f"  ✅ 录屏文件可访问")
                            content_type = video_response.headers.get('Content-Type', 'unknown')
                            print(f"  Content-Type: {content_type}")
                        else:
                            print(f"  ⚠️  录屏文件不可访问: {video_response.status_code}")
                    except Exception as e:
                        print(f"  ⚠️  无法检查录屏文件: {e}")
                else:
                    print(f"  ❌ URL格式错误，期望前缀: {expected_prefix}")
            
            return True, recordings
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            try:
                error_info = response.json()
                print(f"错误信息: {error_info.get('message', 'unknown')}")
            except:
                print(f"响应内容: {response.text}")
            return False, []
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False, []

def test_exam_violations_api(server_url, exam_id):
    """测试考试违规记录API"""
    print(f"\n=== 测试考试违规记录API ===")
    print(f"考试ID: {exam_id}")
    
    try:
        # 测试违规记录API
        violations_url = f"{server_url}/api/exams/{exam_id}/violations"
        response = requests.get(violations_url, timeout=10)
        
        print(f"违规API URL: {violations_url}")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            violations = response.json()
            
            print("✅ 违规记录API调用成功")
            print(f"违规记录数量: {len(violations)}")
            
            # 检查违规记录信息
            for i, violation in enumerate(violations[:3]):  # 只检查前3个
                print(f"\n违规记录 {i+1}:")
                print(f"  学生: {violation.get('username', 'unknown')}")
                print(f"  时间: {violation.get('timestamp', 'unknown')}")
                print(f"  原因: {violation.get('reason', 'unknown')}")
                
                screenshot_url = violation.get('screenshot_url')
                if screenshot_url:
                    print(f"  截图URL: {screenshot_url}")
                    
                    # 验证URL格式
                    expected_prefix = f"/screenshots/{exam_id}/"
                    if screenshot_url.startswith(expected_prefix):
                        print(f"  ✅ 截图URL格式正确")
                        
                        # 测试实际访问违规截图
                        full_url = f"{server_url}{screenshot_url}"
                        try:
                            img_response = requests.get(full_url, timeout=5)
                            if img_response.status_code == 200:
                                print(f"  ✅ 违规截图可访问")
                            else:
                                print(f"  ⚠️  违规截图不可访问: {img_response.status_code}")
                        except Exception as e:
                            print(f"  ⚠️  无法检查违规截图: {e}")
                    else:
                        print(f"  ❌ 截图URL格式错误，期望前缀: {expected_prefix}")
                else:
                    print(f"  ℹ️  无截图")
            
            return True, violations
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            try:
                error_info = response.json()
                print(f"错误信息: {error_info.get('message', 'unknown')}")
            except:
                print(f"响应内容: {response.text}")
            return False, []
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False, []

def get_test_data(server_url):
    """获取测试数据"""
    print(f"\n=== 获取测试数据 ===")
    
    try:
        # 获取考试列表
        exams_response = requests.get(f"{server_url}/api/exams", timeout=10)
        if exams_response.status_code != 200:
            print("❌ 无法获取考试列表")
            return None, None
        
        exams = exams_response.json()
        if not exams:
            print("⚠️  没有可用的考试")
            return None, None
        
        # 选择第一个考试
        exam = exams[0]
        exam_id = exam['id']
        print(f"使用考试: {exam.get('name', 'unknown')} (ID: {exam_id})")
        
        # 获取学生列表
        students_response = requests.get(f"{server_url}/api/exams/{exam_id}/students", timeout=10)
        if students_response.status_code != 200:
            print("❌ 无法获取学生列表")
            return exam_id, None
        
        students = students_response.json()
        if not students:
            print("⚠️  该考试没有学生")
            return exam_id, None
        
        # 选择第一个学生
        student = students[0]
        student_id = student['student_id']
        print(f"使用学生: {student.get('student_name', 'unknown')} (ID: {student_id})")
        
        return exam_id, student_id
        
    except Exception as e:
        print(f"❌ 获取测试数据失败: {e}")
        return None, None

def main():
    """主测试函数"""
    print("学生媒体文件API测试")
    print("=" * 50)
    
    # 配置
    server_url = "http://127.0.0.1:5000"
    exam_id = None
    student_id = None
    
    # 允许用户自定义配置
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    if len(sys.argv) > 2:
        exam_id = int(sys.argv[2])
    if len(sys.argv) > 3:
        student_id = sys.argv[3]
    
    print(f"服务器地址: {server_url}")
    
    # 如果没有指定考试和学生，自动获取
    if not exam_id or not student_id:
        exam_id, student_id = get_test_data(server_url)
        if not exam_id:
            print("❌ 无法获取测试数据，测试终止")
            return
    
    print(f"测试考试ID: {exam_id}")
    print(f"测试学生ID: {student_id}")
    print("-" * 50)
    
    # 1. 测试学生截图API
    print("\n" + "="*50)
    print("1. 测试学生截图API")
    screenshots_success, screenshots = test_student_screenshots_api(server_url, exam_id, student_id)
    
    # 2. 测试学生录屏API
    print("\n" + "="*50)
    print("2. 测试学生录屏API")
    recordings_success, recordings = test_student_recordings_api(server_url, exam_id, student_id)
    
    # 3. 测试违规记录API
    print("\n" + "="*50)
    print("3. 测试违规记录API")
    violations_success, violations = test_exam_violations_api(server_url, exam_id)
    
    # 4. 总结
    print("\n" + "="*50)
    print("=== 测试总结 ===")
    
    success_count = 0
    total_tests = 3
    
    if screenshots_success:
        success_count += 1
        print(f"✅ 学生截图API正常 (找到 {len(screenshots)} 个截图)")
    else:
        print("❌ 学生截图API异常")
    
    if recordings_success:
        success_count += 1
        print(f"✅ 学生录屏API正常 (找到 {len(recordings)} 个录屏)")
    else:
        print("❌ 学生录屏API异常")
    
    if violations_success:
        success_count += 1
        print(f"✅ 违规记录API正常 (找到 {len(violations)} 个违规记录)")
    else:
        print("❌ 违规记录API异常")
    
    print(f"\n测试通过率: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 所有媒体文件API功能正常！")
        print("\nAPI端点总结:")
        print(f"- 学生截图: /api/exams/{{exam_id}}/students/{{student_id}}/screenshots")
        print(f"- 学生录屏: /api/exams/{{exam_id}}/students/{{student_id}}/recordings")
        print(f"- 违规记录: /api/exams/{{exam_id}}/violations")
        print(f"- 截图文件: /screenshots/{{exam_id}}/{{filename}}")
        print(f"- 录屏文件: /recordings/{{exam_id}}/{{filename}}")
        print("\n✅ 基于exam_id的媒体文件API已正确实现")
    else:
        print("⚠️  部分API功能异常，请检查服务器配置和文件存在性")

if __name__ == "__main__":
    main()
