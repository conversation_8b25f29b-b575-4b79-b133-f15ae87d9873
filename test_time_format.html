<!DOCTYPE html>
<html>
<head>
    <title>时间格式化测试</title>
</head>
<body>
    <h1>时间格式化测试</h1>
    <div id="test-results"></div>

    <script>
        // 格式化时间为中文格式：xx年xx月xx日xx时xx分
        function formatChineseDateTime(dateTimeStr) {
            if (!dateTimeStr) return '';
            
            try {
                // 处理不同的时间格式
                let date;
                if (dateTimeStr.includes('T')) {
                    // ISO格式：2024-01-15T14:30:00
                    date = new Date(dateTimeStr);
                } else if (dateTimeStr.includes('-') && dateTimeStr.includes(' ')) {
                    // 标准格式：2024-01-15 14:30:00
                    date = new Date(dateTimeStr);
                } else {
                    // 其他格式
                    date = new Date(dateTimeStr);
                }
                
                if (isNaN(date.getTime())) {
                    return dateTimeStr; // 如果解析失败，返回原字符串
                }
                
                const year = date.getFullYear();
                const month = date.getMonth() + 1;
                const day = date.getDate();
                const hour = date.getHours();
                const minute = date.getMinutes();
                
                return `${year}年${month}月${day}日${hour}时${minute.toString().padStart(2, '0')}分`;
            } catch (error) {
                console.error('时间格式化错误:', error, dateTimeStr);
                return dateTimeStr; // 如果出错，返回原字符串
            }
        }

        // 测试不同的时间格式
        const testCases = [
            '2024-01-15T14:30:00',
            '2024-01-15 14:30:00',
            '2024-12-25T09:05:00',
            '2024-12-25 23:59:00',
            '2024-07-30T16:48:03'
        ];

        let results = '<h2>测试结果：</h2>';
        testCases.forEach(testCase => {
            const formatted = formatChineseDateTime(testCase);
            results += `<p><strong>输入：</strong>${testCase} <br><strong>输出：</strong>${formatted}</p>`;
        });

        document.getElementById('test-results').innerHTML = results;
    </script>
</body>
</html>
