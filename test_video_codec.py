#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试视频编码器可用性
"""

import cv2
import numpy as np
import os

def test_video_codecs():
    """测试各种视频编码器的可用性"""
    print("测试视频编码器可用性...")
    
    # 测试编码器列表
    codecs = ['H264', 'XVID', 'mp4v', 'avc1', 'H265']
    
    # 创建测试视频参数
    width, height = 1920, 1080
    fps = 10
    duration = 2  # 2秒测试视频
    
    for codec in codecs:
        print(f"\n测试编码器: {codec}")
        try:
            # 创建测试视频文件
            test_file = f"test_{codec}.mp4"
            
            # 尝试创建编码器
            fourcc = cv2.VideoWriter_fourcc(*codec)
            writer = cv2.VideoWriter(test_file, fourcc, fps, (width, height))
            
            if writer.isOpened():
                print(f"  ✓ {codec} 编码器可用")
                
                # 写入一些测试帧
                for i in range(fps * duration):
                    # 创建彩色测试帧
                    frame = np.zeros((height, width, 3), dtype=np.uint8)
                    # 添加一些颜色变化
                    frame[:, :, 0] = (i * 25) % 256  # 蓝色通道
                    frame[:, :, 1] = (i * 50) % 256  # 绿色通道
                    frame[:, :, 2] = (i * 75) % 256  # 红色通道
                    
                    writer.write(frame)
                
                writer.release()
                
                # 检查文件是否创建成功
                if os.path.exists(test_file):
                    file_size = os.path.getsize(test_file)
                    print(f"  ✓ 测试视频创建成功: {test_file} ({file_size} bytes)")
                    
                    # 尝试读取视频文件
                    cap = cv2.VideoCapture(test_file)
                    if cap.isOpened():
                        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                        actual_fps = cap.get(cv2.CAP_PROP_FPS)
                        print(f"  ✓ 视频可读取: {frame_count} 帧, {actual_fps:.1f} FPS")
                        cap.release()
                    else:
                        print(f"  ✗ 视频无法读取")
                    
                    # 清理测试文件
                    #os.remove(test_file)
                else:
                    print(f"  ✗ 测试视频文件未创建")
            else:
                print(f"  ✗ {codec} 编码器不可用")
                
        except Exception as e:
            print(f"  ✗ {codec} 编码器测试失败: {e}")

def check_opencv_build_info():
    """检查OpenCV构建信息"""
    print("\nOpenCV 构建信息:")
    print(f"OpenCV 版本: {cv2.__version__}")
    
    # 检查可用的后端
    backends = [
        cv2.CAP_FFMPEG,
        cv2.CAP_GSTREAMER,
        cv2.CAP_MSMF,
        cv2.CAP_DSHOW,
        cv2.CAP_V4L2
    ]
    
    backend_names = {
        cv2.CAP_FFMPEG: 'FFMPEG',
        cv2.CAP_GSTREAMER: 'GStreamer',
        cv2.CAP_MSMF: 'Media Foundation',
        cv2.CAP_DSHOW: 'DirectShow',
        cv2.CAP_V4L2: 'Video4Linux2'
    }
    
    for backend in backends:
        try:
            cap = cv2.VideoCapture(0, backend)
            if cap.isOpened():
                print(f"  ✓ {backend_names.get(backend, str(backend))} 后端可用")
            cap.release()
        except:
            print(f"  ✗ {backend_names.get(backend, str(backend))} 后端不可用")

if __name__ == "__main__":
    check_opencv_build_info()
    test_video_codecs()
    print("\n测试完成！") 