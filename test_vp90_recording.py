#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试VP90.webm编码器录制功能
"""

import os
import sys
import time
import cv2
import numpy as np
import mss
from datetime import datetime

def test_vp90_recording():
    """测试VP90.webm录制功能"""
    print("VP90.webm编码器测试")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = "test_vp90"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 测试参数
    fps = 10
    duration = 30  # 录制5秒
    width = 1920
    height = 1080
    
    # 输出文件路径
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = os.path.join(test_dir, f"test_vp90_{timestamp}.webm")
    
    print(f"输出文件: {output_path}")
    print(f"录制参数: {width}x{height}, {fps}fps, {duration}秒")
    
    try:
        # 测试VP90编码器
        fourcc = cv2.VideoWriter_fourcc(*'VP90')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        if not out.isOpened():
            print("❌ 无法创建VP90编码器")
            return False
        
        print("✅ VP90编码器创建成功")
        
        # 创建屏幕捕获对象
        sct = mss.mss()
        monitor = sct.monitors[1]  # 主显示器
        
        print("开始录制...")
        start_time = time.time()
        frame_count = 0
        
        while time.time() - start_time < duration:
            # 捕获屏幕
            screenshot = sct.grab(monitor)
            frame = np.array(screenshot)
            
            # 转换颜色空间 BGR -> RGB
            frame = cv2.cvtColor(frame, cv2.COLOR_BGRA2BGR)
            
            # 调整尺寸
            frame = cv2.resize(frame, (width, height))
            
            # 写入帧
            out.write(frame)
            frame_count += 1
            
            # 显示进度
            elapsed = time.time() - start_time
            if frame_count % fps == 0:
                print(f"录制进度: {elapsed:.1f}s / {duration}s")
        
        # 释放资源
        out.release()
        sct.close()
        
        # 检查输出文件
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"✅ 录制完成: {output_path}")
            print(f"文件大小: {file_size:,} bytes")
            print(f"实际帧数: {frame_count}")
            print(f"预期帧数: {fps * duration}")
            
            # 验证文件可以读取
            cap = cv2.VideoCapture(output_path)
            if cap.isOpened():
                actual_fps = cap.get(cv2.CAP_PROP_FPS)
                actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                actual_frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                
                print(f"✅ 文件验证成功:")
                print(f"  实际FPS: {actual_fps}")
                print(f"  实际尺寸: {actual_width}x{actual_height}")
                print(f"  实际帧数: {actual_frame_count}")
                
                cap.release()
                return True
            else:
                print("❌ 无法读取录制的文件")
                return False
        else:
            print("❌ 输出文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 录制失败: {e}")
        return False

def test_browser_compatibility():
    """测试浏览器兼容性"""
    print("\n浏览器兼容性测试")
    print("=" * 50)
    
    # 检查文件是否存在
    test_dir = "test_vp90"
    if not os.path.exists(test_dir):
        print("❌ 测试目录不存在，请先运行录制测试")
        return
    
    # 查找最新的测试文件
    webm_files = [f for f in os.listdir(test_dir) if f.endswith('.webm')]
    if not webm_files:
        print("❌ 未找到webm测试文件")
        return
    
    latest_file = max(webm_files, key=lambda x: os.path.getctime(os.path.join(test_dir, x)))
    file_path = os.path.join(test_dir, latest_file)
    
    print(f"测试文件: {file_path}")
    
    # 检查文件信息
    cap = cv2.VideoCapture(file_path)
    if cap.isOpened():
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps if fps > 0 else 0
        
        print(f"✅ 文件信息:")
        print(f"  格式: WebM (VP9)")
        print(f"  尺寸: {width}x{height}")
        print(f"  FPS: {fps}")
        print(f"  帧数: {frame_count}")
        print(f"  时长: {duration:.2f}秒")
        
        # 检查浏览器兼容性
        print(f"\n🌐 浏览器兼容性:")
        print(f"  ✅ Chrome/Edge: 支持VP9/WebM")
        print(f"  ✅ Firefox: 支持VP9/WebM")
        print(f"  ⚠️  Safari: 部分支持VP9/WebM")
        print(f"  ⚠️ IE: 不支持VP9/WebM")
        
        cap.release()
    else:
        print("❌ 无法读取文件")

if __name__ == "__main__":
    print("VP90.webm编码器测试工具")
    print("=" * 60)
    
    # 检查OpenCV版本
    print(f"OpenCV版本: {cv2.__version__}")
    
    # 运行录制测试
    success = test_vp90_recording()
    
    if success:
        # 运行兼容性测试
        test_browser_compatibility()
        
        print(f"\n🎉 测试完成！VP90.webm编码器工作正常")
        print(f"建议: 在考试监控系统中使用VP90.webm格式以获得最佳浏览器兼容性")
    else:
        print(f"\n❌ 测试失败！请检查编码器配置") 